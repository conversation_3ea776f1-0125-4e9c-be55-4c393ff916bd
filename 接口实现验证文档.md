# 机构资质文件接口实现验证文档

## 实现概述

根据接口文档要求，已成功在 `AgencyController.java` 中实现了两个新的接口：

### 1. 获取机构资质文件列表接口

**接口路径**: `GET /publicbiz/agency/qualification/list`

**实现文件**:
- Controller: `AgencyController.getQualificationList()`
- Service: `AgencyQualificationService.getQualificationList()`
- VO: `AgencyQualificationListReqVO`, `AgencyQualificationListRespVO`

**核心功能**:
- 根据机构ID查询资质文件列表
- 支持按文件类型过滤（可选参数）
- 返回完整的文件信息，包括文件URL、大小、扩展名等

### 2. 批量更新机构资质文件接口

**接口路径**: `PUT /publicbiz/agency/qualification/batch-update`

**实现文件**:
- Controller: `AgencyController.batchUpdateQualifications()`
- Service: `AgencyQualificationService.batchUpdateQualifications()`
- VO: `AgencyQualificationBatchUpdateReqVO`

**核心功能**:
- 支持批量更新多个机构的资质文件
- 对于每个文件，先删除相同agencyId和fileType的旧记录，再插入新记录
- 检查必需文件类型，删除缺失的必需类型记录

**必需文件类型**:
- business_license（营业执照）
- account_opening_permit（开户许可证）
- door_photo（门头照）
- id_card_front（法人身份证人像面）
- id_card_back（法人身份证国徽面）

## 数据库操作

### 新增的Mapper方法

在 `AgencyQualificationMapper` 中新增了以下方法：

1. `selectListByAgencyIdAndFileTypes()` - 根据机构ID和文件类型列表查询
2. `selectByAgencyIdAndFileType()` - 根据机构ID和文件类型查询单个记录
3. `deleteByAgencyIdAndFileType()` - 根据机构ID和文件类型删除记录
4. `deleteByAgencyIdAndFileTypes()` - 根据机构ID和文件类型列表批量删除

### 数据转换

在 `AgencyConvert` 中新增了转换方法：
- `convertQualificationListResp()` - 将DO列表转换为响应VO列表
- `convertQualificationResp()` - 将DO转换为响应VO

## 权限控制

两个接口都添加了适当的权限控制：
- 查询接口: `@PreAuthorize("@ss.hasPermission('publicbiz:agency:query')")`
- 更新接口: `@PreAuthorize("@ss.hasPermission('publicbiz:agency:update')")`

## 事务处理

批量更新接口使用了 `@Transactional` 注解，确保数据一致性。

## 参数验证

所有接口都使用了 `@Valid` 注解进行参数验证，确保输入数据的有效性。

## 接口测试建议

### 1. 获取机构资质文件列表接口测试

```bash
# 测试1: 查询指定机构的所有资质文件
GET /publicbiz/agency/qualification/list?agencyId=1001

# 测试2: 查询指定机构的特定类型资质文件
GET /publicbiz/agency/qualification/list?agencyId=1001&fileType=business_license&fileType=qualification_cert
```

### 2. 批量更新机构资质文件接口测试

```json
PUT /publicbiz/agency/qualification/batch-update
{
  "files": [
    {
      "agencyId": 1001,
      "fileType": "business_license",
      "fileCategory": "business_license",
      "fileDescription": "营业执照扫描件",
      "fileName": "营业执照更新版.jpg",
      "fileUrl": "https://example.com/files/a/business_license_new.jpg",
      "fileSize": 620000,
      "fileExtension": "jpg",
      "sortOrder": 1
    },
    {
      "agencyId": 1001,
      "fileType": "qualification_cert",
      "fileCategory": "qualification_cert",
      "fileDescription": "资质证书扫描件",
      "fileName": "资质证书更新版.pdf",
      "fileUrl": "https://example.com/files/a/qualification_cert_new.pdf",
      "fileSize": 1200000,
      "fileExtension": "pdf",
      "sortOrder": 2
    }
  ]
}
```

## 实现特点

1. **完全符合接口文档规范**: 所有字段和业务逻辑都严格按照接口文档实现
2. **数据库字段一致性**: 与 `publicbiz_agency_qualification` 表结构完全对应
3. **业务逻辑完整**: 实现了复杂的批量更新逻辑和必需文件类型检查
4. **代码风格统一**: 遵循项目现有的代码风格和架构模式
5. **错误处理完善**: 使用项目统一的错误处理机制
6. **权限控制严格**: 添加了适当的权限验证

## 注意事项

1. 批量更新接口会删除缺失的必需文件类型记录，请确保前端正确处理这一逻辑
2. 文件类型枚举值必须与数据库表注释中的定义保持一致
3. 建议在生产环境部署前进行充分的集成测试
