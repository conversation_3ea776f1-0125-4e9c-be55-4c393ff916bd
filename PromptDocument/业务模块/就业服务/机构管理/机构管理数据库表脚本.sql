--机构管理相关表
机构主表:publicbiz_agency
机构资质文件表：publicbiz_agency_qualification

-- ==================== 机构管理相关表 ====================
``` sql
-- 机构主表
CREATE TABLE `publicbiz_agency` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 机构基本信息
  `agency_no` VARCHAR(50) NOT NULL COMMENT '机构编号',
  `agency_name` VARCHAR(200) NOT NULL COMMENT '机构全称',
  `agency_short_name` VARCHAR(100) COMMENT '机构简称',
  `agency_type` VARCHAR(30) NOT NULL COMMENT '机构类型：cooperation-合作/competitor-竞争对手/other-其他',
  
  -- 法人信息
  `legal_representative` VARCHAR(50) COMMENT '法人代表',
  `unified_social_credit_code` VARCHAR(50) COMMENT '统一社会信用代码',
  `establishment_date` DATE COMMENT '成立日期',
  
  -- 地址信息
  `registered_address` VARCHAR(500) COMMENT '注册地址',
  `operating_address` VARCHAR(500) COMMENT '经营地址',
  
  -- 经营范围
  `business_scope` TEXT COMMENT '经营范围',
  
  -- 申请人信息
  `applicant_name` VARCHAR(50) COMMENT '申请人姓名',
  `applicant_phone` VARCHAR(20) COMMENT '申请人电话',
  `application_time` DATETIME COMMENT '申请时间',
  
  -- 联系信息及地址
  `contact_person` VARCHAR(50) COMMENT '联系人',
  `contact_phone` VARCHAR(20) COMMENT '联系电话',
  `contact_email` VARCHAR(100) COMMENT '联系邮箱',
  `agency_address` VARCHAR(500) COMMENT '机构地址',
  `province_code` VARCHAR(50) COMMENT '省份code',
  `province` VARCHAR(50) COMMENT '省份',
  `city_code` VARCHAR(50) COMMENT '城市code',
  `city` VARCHAR(50) COMMENT '城市',
  `district_code` VARCHAR(50) COMMENT '区县code',
  `district` VARCHAR(50) COMMENT '区县',
  `street_code` VARCHAR(100) COMMENT '街道',
  `street` VARCHAR(100) COMMENT '街道',
  `detail_address` VARCHAR(200) COMMENT '详细地址',
  
   -- 经纬度信息
  `longitude` DECIMAL(10,7) COMMENT '经度',
  `latitude` DECIMAL(10,7) COMMENT '纬度',
  `location_accuracy` VARCHAR(20) COMMENT '位置精度：high-高精度/medium-中等精度/low-低精度',
  
  -- 合作信息
  `cooperation_status` VARCHAR(20) NOT NULL DEFAULT 'cooperating' COMMENT '合作状态：cooperating-合作中/suspended-已暂停/terminated-已终止,pending-待审核',
  `contract_no` VARCHAR(50) COMMENT '合同编号',
  `contract_start_date` DATE COMMENT '合同开始日期',
  `contract_end_date` DATE COMMENT '合同结束日期',
  `commission_rate` DECIMAL(5,2) COMMENT '佣金比例',
  
  -- 审核信息
  `review_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '审核状态：pending-待审核/approved-已通过/rejected-已拒绝',
  `reviewer` VARCHAR(64) COMMENT '审核人',
  `review_time` DATETIME COMMENT '审核时间',
  `review_remark` TEXT COMMENT '审核备注',
  
  -- 状态信息
  `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态：active-正常/inactive-停用/pending-待审核',
  
  -- 备注信息
  `remark` TEXT COMMENT '备注',
  
  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agency_no` (`agency_no`),
  UNIQUE KEY `uk_unified_social_credit_code` (`unified_social_credit_code`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_agency_name` (`agency_name`),
  KEY `idx_agency_type` (`agency_type`),
  KEY `idx_legal_representative` (`legal_representative`),
  KEY `idx_establishment_date` (`establishment_date`),
  KEY `idx_province_code` (`province_code`),
  KEY `idx_city_code` (`city_code`),
  KEY `idx_longitude_latitude` (`longitude`, `latitude`),
  KEY `idx_applicant_name` (`applicant_name`),
  KEY `idx_application_time` (`application_time`),
  KEY `idx_cooperation_status` (`cooperation_status`),
  KEY `idx_contract_no` (`contract_no`),
  KEY `idx_review_status` (`review_status`),
  KEY `idx_reviewer` (`reviewer`),
  KEY `idx_review_time` (`review_time`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='机构主表信息';


CREATE TABLE `publicbiz_agency_qualification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `agency_id` bigint(20) NOT NULL COMMENT '机构ID',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型：business_license-营业执照/qualification_cert-资质证书/account_opening_permit/开户许可证/contract-合同文件/other-其他/human_resources人力资源服务许可证/door_photo 门头照/organizational_structure 组织机构代码证书/id_card_front: 法人身份证人像面/id_card_back:法人身份证国徽面',
  `file_category` varchar(50) DEFAULT NULL COMMENT '文件分类：business_license-营业执照/account_permit-开户许可证/id_card_front-身份证正面/id_card_back-身份证反面/org_code-组织机构代码证',
  `file_description` varchar(200) DEFAULT NULL COMMENT '文件描述',
  `file_name` varchar(200) NOT NULL COMMENT '文件名',
  `file_url` varchar(500) NOT NULL COMMENT '文件URL',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
  `file_extension` varchar(20) DEFAULT NULL COMMENT '文件扩展名',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序，数字越小越靠前',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-有效，0-无效',
  PRIMARY KEY (`id`),
  KEY `idx_agency_id` (`agency_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=20029 DEFAULT CHARSET=utf8mb4 COMMENT='机构资质文件表';
```


