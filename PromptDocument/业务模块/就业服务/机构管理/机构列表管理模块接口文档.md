# 机构列表管理模块API接口文档

## 1. 模块概述

本模块提供机构管理相关的API接口，包括机构列表查询、审核详情查询、审核操作等功能。

### 数据库表结构

- **主表**：`publicbiz_agency` - 机构主表
- **关联表**：`publicbiz_agency_qualification` - 机构资质文件表

---

## 2. 接口列表

### 2.1 机构列表查询接口

**接口地址**：`GET /publicbiz/agency/page`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| pageNum | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页条数，默认20 |
| keyword | string | 否 | 机构名称或机构ID模糊搜索 |
| cooperationStatus | string | 否 | 合作状态：cooperating-合作中/suspended-已暂停/terminated-已终止 |
| reviewStatus | string | 否 | 审核状态：pending-待审核/approved-已通过/rejected-已拒绝 |
| district | string | 否 | 所属区县 |
| agencyNo | string | 否 | 机构编码 |

**响应字段**：

| 字段名                    | 类型   | 说明       |
| ------------------------- | ------ | ---------- |
| code                      | int    | 响应状态码 |
| message                   | string | 响应消息   |
| data                      | array  | 机构列表   |
| data[].id                 | long   | 机构ID     |
| data[].agencyName         | string | 机构名称   |
| data[].agencyNo           | string | 机构编号   |
| data[].cooperationStatus  | string | 合作状态   |
| data[].reviewStatus       | string | 审核状态   |
| data[].contactPerson      | string | 联系人     |
| data[].contactPhone       | string | 联系电话   |
| data[].province           | string | 省份       |
| data[].city               | string | 城市       |
| data[].district           | string | 区县       |
| data[].applicationTime    | string | 申请时间   |
| total                     | int    | 总条数     |
| pageNum                   | int    | 当前页码   |
| pageSize                  | int    | 每页条数   |

**响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "agencyName": "测试机构",
      "agencyNo": "AG001",
      "cooperationStatus": "cooperating",
      "reviewStatus": "pending",
      "contactPerson": "张三",
      "contactPhone": "***********",
      "province": "四川省",
      "city": "成都市",
      "district": "高新区",
      "applicationTime": "2024-01-01 10:00:00"
    }
  ],
  "total": 100,
  "pageNum": 1,
  "pageSize": 20
  
}
```

### 2.2 机构列表查询接口（不分页）

**接口地址**：`GET /publicbiz/agency/list`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| keyword | string | 否 | 机构名称或机构ID模糊搜索 |
| cooperationStatus | string | 否 | 合作状态：cooperating-合作中/suspended-已暂停/terminated-已终止 |
| reviewStatus | string | 否 | 审核状态：pending-待审核/approved-已通过/rejected-已拒绝 |
| district | string | 否 | 所属区县 |
| agencyNo | string | 否 | 机构编码 |

**响应字段**：

| 字段名                    | 类型   | 说明       |
| ------------------------- | ------ | ---------- |
| code                      | int    | 响应状态码 |
| message                   | string | 响应消息   |
| data                      | array  | 机构列表   |
| data[].id                 | long   | 机构ID     |
| data[].agencyName         | string | 机构名称   |
| data[].agencyNo           | string | 机构编号   |
| data[].cooperationStatus  | string | 合作状态   |
| data[].reviewStatus       | string | 审核状态   |
| data[].contactPerson      | string | 联系人     |
| data[].contactPhone       | string | 联系电话   |
| data[].province           | string | 省份       |
| data[].city               | string | 城市       |
| data[].district           | string | 区县       |
| data[].applicationTime    | string | 申请时间   |

**响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "agencyName": "测试机构",
      "agencyNo": "AG001",
      "cooperationStatus": "cooperating",
      "reviewStatus": "pending",
      "contactPerson": "张三",
      "contactPhone": "***********",
      "province": "四川省",
      "city": "成都市",
      "district": "高新区",
      "applicationTime": "2024-01-01 10:00:00"
    },
    {
      "id": 2,
      "agencyName": "测试机构B",
      "agencyNo": "AG002",
      "cooperationStatus": "suspended",
      "reviewStatus": "approved",
      "contactPerson": "李雷",
      "contactPhone": "13800138001",
      "province": "四川省",
      "city": "成都市",
      "district": "武侯区",
      "applicationTime": "2024-01-02 11:00:00"
    }
  ]
}
```

### 2.3 审核详情查询接口

**接口地址**：`GET /publicbiz/agency/get/{id}`

**路径参数**：

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | long | 是   | 机构ID |

**响应字段**：

| 字段名                          | 类型   | 说明             |
| ------------------------------- | ------ | ---------------- |
| code                            | int    | 响应状态码       |
| message                         | string | 响应消息         |
| data                            | object | 响应数据         |
| data.id                         | long   | 机构ID           |
| data.agencyName                 | string | 机构名称         |
| data.agencyShortName            | string | 机构简称         |
| data.agencyNo                   | string | 机构编号         |
| data.agencyType                 | string | 机构类型         |
| data.legalRepresentative        | string | 法人代表         |
| data.unifiedSocialCreditCode    | string | 统一社会信用代码 |
| data.establishmentDate          | string | 成立日期         |
| data.registeredAddress          | string | 注册地址         |
| data.operatingAddress           | string | 经营地址         |
| data.businessScope              | string | 经营范围         |
| data.applicantName              | string | 申请人姓名       |
| data.applicantPhone             | string | 申请人电话       |
| data.applicationTime            | string | 申请时间         |
| data.contactPerson              | string | 联系人           |
| data.contactPhone               | string | 联系电话         |
| data.contactEmail               | string | 联系邮箱         |
| data.province                   | string | 省份             |
| data.city                       | string | 城市             |
| data.district                   | string | 区县             |
| data.cooperationStatus          | string | 合作状态         |
| data.reviewStatus               | string | 审核状态         |
| data.qualifications             | array  | 资质文件列表     |
| data.qualifications[].fileType  | string | 文件类型         |
| data.qualifications[].fileName  | string | 文件名           |
| data.qualifications[].fileUrl   | string | 文件URL          |

**响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "agencyName": "测试机构",
    "agencyShortName": "测试",
    "agencyNo": "AG001",
    "agencyType": "cooperation",
    "legalRepresentative": "李四",
    "unifiedSocialCreditCode": "91110000123456789X",
    "establishmentDate": "2020-01-01",
    "registeredAddress": "四川省成都市高新区",
    "operatingAddress": "四川省成都市高新区",
    "businessScope": "家政服务",
    "applicantName": "张三",
    "applicantPhone": "***********",
    "applicationTime": "2024-01-01 10:00:00",
    "contactPerson": "张三",
    "contactPhone": "***********",
    "contactEmail": "<EMAIL>",
    "province": "四川省",
    "city": "成都市",
    "district": "高新区",
    "cooperationStatus": "cooperating",
    "reviewStatus": "pending",
    "qualifications": [
      {
        "fileType": "business_license",
        "fileName": "营业执照.jpg",
        "fileUrl": "https://example.com/files/business_license.jpg"
      }
    ]
  }
}
```

### 2.4 审核更新接口

**接口地址**：`PUT /publicbiz/agency/update`

**请求参数**：

| 参数名       | 类型   | 必填 | 说明                                      |
| ------------ | ------ | ---- | ----------------------------------------- |
| id           | long   | 是   | 机构ID                                    |
| reviewStatus | string | 是   | 审核状态：approved-已通过/rejected-已拒绝 |
| reviewRemark | string | 否   | 审核备注                                  |

**请求示例**：

```json
{
  "id": 1,
  "reviewStatus": "approved",
  "reviewRemark": "审核通过，资料齐全"
}
```

**响应字段**：

| 字段名  | 类型   | 说明                         |
| ------- | ------ | ---------------------------- |
| code    | int    | 响应状态码                   |
| message | string | 响应消息                     |
| data    | object | 响应数据（审核接口返回null） |

**响应示例**：

```json
{
  "code": 200,
  "message": "审核成功",
  "data": null
}
```

### 2.5 获取机构资质文件列表接口

**接口地址**：`GET /publicbiz/agency/qualification/list`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| agencyId | long | 是 | 机构ID |
| fileType | array | 否 | 文件类型数组，可选值：business_license-营业执照/qualification_cert-资质证书/account_opening_permit-开户许可证/contract-合同文件/other-其他/human_resources-人力资源服务许可证/door_photo-门头照/organizational_structure-组织机构代码证书/id_card_front-法人身份证人像面/id_card_back-法人身份证国徽面 |

**响应字段**：

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| code | int | 响应状态码 |
| message | string | 响应消息 |
| data | array | 文件列表 |
| data[].agencyId | long | 机构ID |
| data[].fileType | string | 文件类型，可选值：business_license-营业执照/qualification_cert-资质证书/account_opening_permit-开户许可证/contract-合同文件/other-其他/human_resources-人力资源服务许可证/door_photo-门头照/organizational_structure-组织机构代码证书/id_card_front-法人身份证人像面/id_card_back-法人身份证国徽面 |
| data[].fileCategory | string | 文件分类 |
| data[].fileDescription | string | 文件描述 |
| data[].fileName | string | 文件名 |
| data[].fileUrl | string | 文件URL |
| data[].fileSize | long | 文件大小（单位：字节） |
| data[].fileExtension | string | 文件扩展名 |
| data[].sortOrder | int | 排序序号（数字越小越靠前） |

**响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "agencyId": 1001,
      "fileType": "business_license",
      "fileCategory": "business_license",
      "fileDescription": "营业执照扫描件",
      "fileName": "营业执照A.jpg",
      "fileUrl": "https://example.com/files/a/business_license.jpg",
      "fileSize": 512000,
      "fileExtension": "jpg",
      "sortOrder": 1
    },
    {
      "agencyId": 1001,
      "fileType": "qualification_cert",
      "fileCategory": "qualification_cert",
      "fileDescription": "资质证书扫描件",
      "fileName": "资质证书A.pdf",
      "fileUrl": "https://example.com/files/a/qualification_cert.pdf",
      "fileSize": 1024000,
      "fileExtension": "pdf",
      "sortOrder": 2
    }
  ]
}
```

---

## 3. 数据字典

### 3.1 合作状态 (cooperationStatus)

- cooperating：合作中
- suspended：已暂停
- terminated：已终止
- pending：待审核

### 3.2 审核状态 (reviewStatus)

- pending：待审核
- approved：已通过
- rejected：已拒绝

### 3.3 文件类型 (fileType)

- business_license：营业执照
- qualification_cert：资质证书
- account_opening_permit：开户许可证
- contract：合同文件
- other：其他
- human_resources：人力资源服务许可证
- door_photo：门头照
- organizational_structure：组织机构代码证书
- id_card_front：法人身份证人像面
- id_card_back：法人身份证国徽面

---

## 4. 错误码说明

| 错误码 | 说明           |
| ------ | -------------- |
| 200    | 成功           |
| 400    | 请求参数错误   |
| 401    | 未授权         |
| 403    | 权限不足       |
| 404    | 资源不存在     |
| 500    | 服务器内部错误 |

---

## 5. 测试数据脚本

说明：以下脚本基于 MySQL，覆盖主表 `publicbiz_agency` 与关联表 `publicbiz_agency_qualification`，用于本模块联调/自测。脚本采用显式 `id` 与可重复执行的 UPSERT 写法（ON DUPLICATE KEY UPDATE），便于多次导入。

### 5.1 机构主表 `publicbiz_agency`

```sql
-- 可选：为避免编码问题
SET NAMES utf8mb4;

-- 插入/更新机构示例数据
INSERT INTO `publicbiz_agency` (
  `id`, `agency_no`, `agency_name`, `agency_short_name`, `agency_type`,
  `legal_representative`, `unified_social_credit_code`, `establishment_date`,
  `registered_address`, `operating_address`, `business_scope`,
  `applicant_name`, `applicant_phone`, `application_time`,
  `contact_person`, `contact_phone`, `contact_email`,
  `province_code`, `province`, `city_code`, `city`, `district_code`, `district`, `detail_address`,
  `cooperation_status`, `contract_no`, `contract_start_date`, `contract_end_date`, `commission_rate`,
  `review_status`, `reviewer`, `review_time`, `review_remark`, `status`, `remark`
) VALUES
  (1001, 'AG001', '测试机构A', '测A', 'cooperation',
   '李四', '91510100MA0000011A', '2020-05-20',
   '四川省成都市高新区天府大道100号', '四川省成都市高新区天府大道100号', '家政服务; 劳务派遣',
   '张三', '***********', '2024-01-01 10:00:00',
   '张三', '***********', '<EMAIL>',
   '510000', '四川省', '510100', '成都市', '510109', '高新区', '天府大道100号A座',
   'cooperating', 'HT2024-001', '2024-01-01', '2024-12-31', 10.00,
   'pending', NULL, NULL, NULL, 'active', '样例数据-联调'),
  (1002, 'AG002', '测试机构B', '测B', 'competitor',
   '王五', '91510100MA0000012B', '2019-03-15',
   '四川省成都市武侯区科华北路88号', '四川省成都市武侯区科华北路88号', '保洁服务; 外包服务',
   '李雷', '13800138001', '2024-01-02 11:00:00',
   '李雷', '13800138001', '<EMAIL>',
   '510000', '四川省', '510100', '成都市', '510107', '武侯区', '科华北路88号',
   'suspended', 'HT2024-002', '2024-01-05', '2024-06-30', 8.50,
   'approved', '管理员', '2024-01-10 09:00:00', '资料齐全', 'active', '样例数据-联调'),
  (1003, 'AG003', '测试机构C', '测C', 'other',
   '赵六', '91510100MA0000013C', '2018-07-01',
   '四川省成都市成华区建设北路66号', '四川省成都市成华区建设北路66号', '职业介绍; 培训',
   '韩梅', '13800138002', '2024-01-03 12:30:00',
   '韩梅', '13800138002', '<EMAIL>',
   '510000', '四川省', '510100', '成都市', '510108', '成华区', '建设北路66号',
   'terminated', 'HT2023-003', '2023-03-01', '2023-12-31', 6.00,
   'rejected', '管理员', '2024-01-12 10:30:00', '证照过期', 'inactive', '样例数据-联调'),
  (1004, 'AG004', '测试机构D', '测D', 'cooperation',
   '钱七', '91510100MA0000014D', '2021-10-10',
   '四川省成都市青羊区顺城大街9号', '四川省成都市青羊区顺城大街9号', '家政服务; 灵活用工',
   '周杰', '13800138003', '2024-01-04 09:20:00',
   '周杰', '13800138003', '<EMAIL>',
   '510000', '四川省', '510100', '成都市', '510105', '青羊区', '顺城大街9号',
   'pending', NULL, NULL, NULL, 0.00,
   'pending', NULL, NULL, NULL, 'pending', '样例数据-联调'),
  (1005, 'AG005', '测试机构E', '测E', 'competitor',
   '孙八', '91510100MA0000015E', '2022-02-18',
   '四川省德阳市旌阳区长江西路18号', '四川省德阳市旌阳区长江西路18号', '人力资源外包',
   '吴起', '13800138004', '2024-01-05 14:10:00',
   '吴起', '13800138004', '<EMAIL>',
   '510600', '德阳市', '510600', '德阳市', '510603', '旌阳区', '长江西路18号',
   'cooperating', 'HT2024-005', '2024-02-01', '2024-12-31', 12.00,
   'approved', '管理员', '2024-02-10 08:30:00', '通过复核', 'active', '样例数据-联调')
ON DUPLICATE KEY UPDATE
  `agency_name`=VALUES(`agency_name`),
  `agency_short_name`=VALUES(`agency_short_name`),
  `agency_type`=VALUES(`agency_type`),
  `legal_representative`=VALUES(`legal_representative`),
  `unified_social_credit_code`=VALUES(`unified_social_credit_code`),
  `establishment_date`=VALUES(`establishment_date`),
  `registered_address`=VALUES(`registered_address`),
  `operating_address`=VALUES(`operating_address`),
  `business_scope`=VALUES(`business_scope`),
  `applicant_name`=VALUES(`applicant_name`),
  `applicant_phone`=VALUES(`applicant_phone`),
  `application_time`=VALUES(`application_time`),
  `contact_person`=VALUES(`contact_person`),
  `contact_phone`=VALUES(`contact_phone`),
  `contact_email`=VALUES(`contact_email`),
  `province_code`=VALUES(`province_code`),
  `province`=VALUES(`province`),
  `city_code`=VALUES(`city_code`),
  `city`=VALUES(`city`),
  `district_code`=VALUES(`district_code`),
  `district`=VALUES(`district`),
  `detail_address`=VALUES(`detail_address`),
  `cooperation_status`=VALUES(`cooperation_status`),
  `contract_no`=VALUES(`contract_no`),
  `contract_start_date`=VALUES(`contract_start_date`),
  `contract_end_date`=VALUES(`contract_end_date`),
  `commission_rate`=VALUES(`commission_rate`),
  `review_status`=VALUES(`review_status`),
  `reviewer`=VALUES(`reviewer`),
  `review_time`=VALUES(`review_time`),
  `review_remark`=VALUES(`review_remark`),
  `status`=VALUES(`status`),
  `remark`=VALUES(`remark`),
  `update_time`=CURRENT_TIMESTAMP;
```

### 5.2 机构资质文件表 `publicbiz_agency_qualification`

```sql
-- 为上述机构补充资质文件样例
INSERT INTO `publicbiz_agency_qualification` (
  `id`, `agency_id`, `file_type`, `file_name`, `file_url`, `file_size`, `file_extension`, `sort_order`, `status`
) VALUES
  (20001, 1001, 'business_license', '营业执照A.jpg', 'https://example.com/files/a/business_license.jpg', 512000, 'jpg', 1, 1),
  (20002, 1001, 'qualification_cert', '资质证书A.pdf', 'https://example.com/files/a/qualification_cert.pdf', 1024000, 'pdf', 2, 1),
  (20003, 1002, 'contract', '合作合同B.pdf', 'https://example.com/files/b/contract.pdf', 204800, 'pdf', 1, 1),
  (20004, 1003, 'door_photo', '门头照C.png', 'https://example.com/files/c/door.png', 350000, 'png', 1, 1),
  (20005, 1004, 'human_resources', '人力资源服务许可证D.pdf', 'https://example.com/files/d/hr_license.pdf', 750000, 'pdf', 1, 1),
  (20006, 1005, 'opening_permit', '开户许可证E.jpg', 'https://example.com/files/e/opening_permit.jpg', 640000, 'jpg', 1, 1),
  (20007, 1005, 'id_card', '法人身份证E-正反面.pdf', 'https://example.com/files/e/id_card.pdf', 280000, 'pdf', 2, 1)
ON DUPLICATE KEY UPDATE
  `file_type`=VALUES(`file_type`),
  `file_name`=VALUES(`file_name`),
  `file_url`=VALUES(`file_url`),
  `file_size`=VALUES(`file_size`),
  `file_extension`=VALUES(`file_extension`),
  `sort_order`=VALUES(`sort_order`),
  `status`=VALUES(`status`),
  `update_time`=CURRENT_TIMESTAMP;
```

提示：
- 若目标库存在自增冲突，可调整 `id` 段（如 11001/21001 开始）或移除 `id` 列改用自增；如移除 `id`，请去掉 UPSERT 以避免重复数据。
- `file_type` 取值需遵循数据字典 3.3 所列枚举。
