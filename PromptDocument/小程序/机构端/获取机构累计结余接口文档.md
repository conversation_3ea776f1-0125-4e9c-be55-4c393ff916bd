# 机构财务中心接口文档

## 概述
本文档描述了机构财务中心页面所需的后端接口，用于获取机构的财务数据和收支流水明细。

## 接口列表

### 1. 获取机构累计结余接口

**接口地址：** `GET /publicbiz/agency/finance/balance`

**接口描述：** 获取当前机构的累计结余金额

**请求参数：**
```json
{
  "agencyId": "string"   // 机构ID（必填）
}
```

**响应数据：**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "balanceAmount": "34260.00",  // 累计结余金额（字符串格式，保留2位小数）
    "currency": "CNY"             // 货币类型
  }
}
```

### 2. 获取月度财务数据接口（合并版）

**接口地址：** `GET /publicbiz/agency/finance/monthly-data`

**接口描述：** 获取指定月份的收支汇总和明细列表，支持分页和筛选

**请求参数：**
```json
{
  "agencyId": "string",     // 机构ID（必填）
  "year": 2025,             // 年份（必填）
  "month": 7,               // 月份（必填，1-12）
  "type": "all",            // 交易类型筛选：all-全部，income-收入，expense-支出（可选）
  "pageNo": 1,              // 页码（可选，默认1）
  "pageSize": 20            // 每页数量（可选，默认20）
}
```

**响应数据：**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    // 月度汇总数据
    "summary": {
      "year": 2025,
      "month": 7,
      "incomeAmount": "10000.00",   // 收入总金额（字符串格式，保留2位小数）
      "expenseAmount": "5800.00",   // 支出总金额（字符串格式，保留2位小数）
      "netAmount": "4200.00"        // 净收入（收入-支出）
    },
    // 交易明细列表
    "transactions": {
      "list": [
        {
          "id": "string",                    // 交易记录ID
          "description": "支出保险订单(在线支付)", // 交易描述
          "time": "2025-07-10 14:09:25",     // 交易时间
          "amount": "-2300.00",              // 交易金额（正数表示收入，负数表示支出）
          "type": "expense",                 // 交易类型：income-收入，expense-支出
          "balance": "在线支付结余: ¥360",    // 账户结余信息
          "orderNo": "DD202507100001",       // 关联订单号（可选）
          "paymentMethod": "online",         // 支付方式：online-在线支付，wechat-微信，alipay-支付宝等
          "category": "insurance",           // 交易分类：insurance-保险，order-订单，commission-佣金等
          "status": "success"                // 交易状态：success-成功，failed-失败，pending-处理中
        }
      ],
      "total": 100,           // 总记录数
      "pageNo": 1,            // 当前页码
      "pageSize": 20,         // 每页数量
      "totalPages": 5         // 总页数
    }
  }
}
```

### 3. 获取机构财务统计接口（可选）

**接口地址：** `GET /publicbiz/agency/finance/statistics`

**接口描述：** 获取机构的财务统计数据，包括年度汇总等

**请求参数：**
```json
{
  "agencyId": "string",  // 机构ID（必填）
  "year": 2025           // 年份（可选，默认当前年份）
}
```

**响应数据：**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "year": 2025,
    "totalIncome": "150000.00",    // 年度总收入
    "totalExpense": "85000.00",    // 年度总支出
    "netIncome": "65000.00",       // 年度净收入
    "monthlyData": [               // 月度数据
      {
        "month": 1,
        "income": "12000.00",
        "expense": "8000.00"
      }
      // ... 其他月份数据
    ]
  }
}
```

## 接口调用示例

### 前端调用示例：

```javascript
// 1. 获取累计结余
const getBalance = async () => {
  const response = await request({
    url: '/publicbiz/agency/finance/balance',
    method: 'GET',
    params: {
      agencyId: 'agency_123'
    }
  })
  return response.data
}

// 2. 获取月度财务数据（合并版）
const getMonthlyData = async (year, month, type = 'all', pageNo = 1) => {
  const response = await request({
    url: '/publicbiz/agency/finance/monthly-data',
    method: 'GET',
    params: {
      agencyId: 'agency_123',
      year: year,
      month: month,
      type: type,
      pageNo: pageNo,
      pageSize: 20
    }
  })
  return response.data
}

// 3. 获取财务统计
const getStatistics = async (year) => {
  const response = await request({
    url: '/publicbiz/agency/finance/statistics',
    method: 'GET',
    params: {
      agencyId: 'agency_123',
      year: year
    }
  })
  return response.data
}
```

## 前端使用示例

```javascript
// 在机构财务中心页面中使用
export default {
  data() {
    return {
      balanceAmount: '0.00',
      currentMonth: '2025年7月',
      expenseAmount: '0.00',
      incomeAmount: '0.00',
      transactions: [],
      pageParams: {
        pageNo: 1,
        pageSize: 20,
        hasMore: true
      }
    }
  },
  
  methods: {
    // 获取累计结余
    async getBalance() {
      try {
        const response = await getBalance()
        this.balanceAmount = response.balanceAmount
      } catch (error) {
        console.error('获取累计结余失败:', error)
      }
    },
    
    // 获取月度数据
    async getMonthlyData(year, month, type = 'all', isRefresh = false) {
      try {
        if (isRefresh) {
          this.pageParams.pageNo = 1
          this.pageParams.hasMore = true
        }
        
        const response = await getMonthlyData(year, month, type, this.pageParams.pageNo)
        
        // 更新汇总数据
        const { summary, transactions } = response
        this.expenseAmount = summary.expenseAmount
        this.incomeAmount = summary.incomeAmount
        
        // 更新明细列表
        if (isRefresh) {
          this.transactions = transactions.list
        } else {
          this.transactions.push(...transactions.list)
        }
        
        // 更新分页信息
        this.pageParams.hasMore = this.transactions.length < transactions.total
        this.pageParams.pageNo++
        
      } catch (error) {
        console.error('获取月度数据失败:', error)
      }
    },
    
    // 确认月份选择
    async confirmSelection() {
      this.currentMonth = `${this.selectedYear}年${this.selectedMonth}月`
      this.hideMonthPicker()
      
      // 获取对应月份的数据
      await this.getMonthlyData(this.selectedYear, this.selectedMonth, 'all', true)
    }
  }
}
```

## 注意事项

1. **权限验证**：所有接口都需要验证用户是否有权限访问该机构的财务数据（通过agencyId进行权限验证）
2. **数据格式**：金额字段统一使用字符串格式，保留2位小数
3. **时间格式**：时间字段使用 `YYYY-MM-DD HH:mm:ss` 格式
4. **分页处理**：交易明细支持分页，建议每页20条记录
5. **筛选功能**：交易明细支持按类型筛选（全部/收入/支出）
6. **错误处理**：接口失败时返回相应的错误码和错误信息
7. **性能优化**：合并接口可以减少网络请求次数，提升页面加载速度

## 优势说明

**合并接口的优势：**
1. **减少网络请求**：一次请求同时获取汇总和明细数据
2. **提升性能**：减少网络延迟，提升页面响应速度
3. **数据一致性**：确保汇总数据和明细数据的时间范围完全一致
4. **简化前端逻辑**：前端只需要调用一个接口即可获取完整数据
5. **降低服务器压力**：减少接口调用次数，降低服务器负载