package cn.bztmaster.cnt.module.talentpool.api.talent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 人才用户标签详情 Response DTO
 *
 * 用于返回人才库用户的详细标签信息。
 *
 * <AUTHOR>
 */
@Data
public class TalentUserTagDetailRespDTO {

    /**
     * 标签ID
     */
    @Schema(description = "标签ID", example = "1001")
    private Long tagId;

    /**
     * 标签类型ID
     */
    @Schema(description = "标签类型ID", example = "1")
    private Long tagTypeId;

    /**
     * 标签名称
     */
    @Schema(description = "标签名称", example = "高级母婴护理")
    private String tagName;

    /**
     * 标签编码
     */
    @Schema(description = "标签编码", example = "ADVANCED_MATERNAL_CARE")
    private String tagCode;

    /**
     * 标签类型名称
     */
    @Schema(description = "标签类型名称", example = "技能认证")
    private String tagTypeName;

    /**
     * 标签类型编码
     */
    @Schema(description = "标签类型编码", example = "SKILL_CERTIFICATION")
    private String tagTypeCode;

    /**
     * 标签描述
     */
    @Schema(description = "标签描述", example = "具备高级母婴护理技能")
    private String description;
}
