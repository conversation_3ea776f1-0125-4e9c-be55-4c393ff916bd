package cn.bztmaster.cnt.module.talentpool.dal.mysql.talent;

import cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserCommentDO;
import org.apache.ibatis.annotations.Mapper;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import java.util.List;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TalentUserCommentMapper extends BaseMapperX<TalentUserCommentDO> {
    @Select("SELECT * FROM talent_user_comment WHERE user_id = #{userId} AND deleted = 0")
    List<TalentUserCommentDO> selectListByUserId(@Param("userId") Long userId);
}