package cn.bztmaster.cnt.module.talentpool.dal.mysql.talent;

import cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserTagDO;
import org.apache.ibatis.annotations.Mapper;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Param;
import cn.bztmaster.cnt.framework.tenant.core.context.TenantContextHolder;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.List;
import java.util.Map;

@Mapper
public interface TalentUserTagMapper extends BaseMapperX<TalentUserTagDO> {
    // 可扩展自定义方法
    default List<TalentUserTagDO> selectListByUserIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return java.util.Collections.emptyList();
        }
        Long tenantId = TenantContextHolder.getTenantId();
        return selectList(new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<TalentUserTagDO>()
                .in(TalentUserTagDO::getUserId, userIds)
                .eq(TalentUserTagDO::getDeleted, false)
                .eq(TalentUserTagDO::getTenantId, tenantId));
    }

    @Select("SELECT * FROM talent_user_tag WHERE user_id = #{userId} AND deleted = 0")
    java.util.List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserTagDO> selectListByUserId(
            @Param("userId") Long userId);

    @Delete("DELETE FROM talent_user_tag WHERE user_id = #{userId}")
    void deleteByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询用户的标签信息（多表关联查询）
     * 关联talent_user_tag、talent_tag_library、talent_tag_type表
     * 
     * @param userId 用户ID
     * @return 标签信息列表，包含tagId、tagName、tagTypeName等字段
     */
    @Select("SELECT " +
            "    ut.tag_id as tagId, " +
            "    ut.tag_type_id as tagTypeId, " +
            "    tl.tag_name as tagName, " +
            "    tl.tag_code as tagCode, " +
            "    tt.type_name as tagTypeName, " +
            "    tt.type_code as tagTypeCode " +
            "FROM talent_user_tag ut " +
            "INNER JOIN talent_tag_library tl ON ut.tag_id = tl.tag_id AND tl.deleted = 0 " +
            "INNER JOIN talent_tag_type tt ON ut.tag_type_id = tt.tag_type_id AND tt.deleted = 0 " +
            "WHERE ut.user_id = #{userId} AND ut.deleted = 0")
    List<Map<String, Object>> selectUserTagsWithDetails(@Param("userId") Long userId);
}