package cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent;

import lombok.Data;
import java.util.Date;
import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;

/**
 * 培训记录表实体
 * 对应表：talent_training
 */
@TableName("talent_training")
@KeySequence("talent_training_seq")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalentTrainingDO extends BaseDO {
    @TableId
    private Long trainingId;
    private Long userId;
    private String provider;
    private String course;
    private Date completeDate;
    private Boolean deleted;
    private String creator;
    private String updater;
    private Long tenantId;
}