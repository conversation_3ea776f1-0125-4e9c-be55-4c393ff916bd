package cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent;

import lombok.Data;
import java.util.Date;
import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;

/**
 * 人才用户数据对象
 *
 * 对应表：talent_user
 *
 * <AUTHOR>
 */
@TableName("talent_user")
@KeySequence("talent_user_seq")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalentUserDO extends BaseDO {
    /**
     * 用户ID
     */
    @TableId
    private Long userId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证号
     */
    private String identityId;
    /**
     * 出生日期
     */
    private Date birthDate;
    /**
     * 性别
     */
    private String gender;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 头像URL
     */
    private String avatarUrl;
    /**
     * OneID状态（正常、待合并、已禁用）
     */
    private String status;
    /**
     * 用户来源
     */
    private String registerSource;
    /**
     * OneID GUID
     */
    private String oneid;
    /**
     * 是否删除
     */
    private Boolean deleted;
    /**
     * 档案完整度百分比
     */
    private Byte completeness;
    /**
     * 租户编号
     */
    private Long tenantId;
    /**
     * 所属机构ID
     */
    private Long orgId;
    /**
     * 所属机构名称
     */
    private String orgName;
    /**
     * 人才来源
     */
    private String talentSource;
    /**
     * 是否为平台自营（0-否，1-是）
     */
    private Boolean isSelfSupport;
}