package cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

/**
 * 标签类型表实体
 * 对应表：talent_tag_type
 */
@TableName("talent_tag_type")
@Data
public class TalentTagTypeDO {
    @TableId
    private Long tagTypeId;
    private String typeCode;
    private String typeName;
    private String description;
    private Boolean deleted;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private Long tenantId;
}