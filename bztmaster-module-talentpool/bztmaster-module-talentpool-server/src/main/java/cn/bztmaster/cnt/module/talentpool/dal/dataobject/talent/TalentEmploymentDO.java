package cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent;

import lombok.Data;
import java.util.Date;
import java.math.BigDecimal;
import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;

/**
 * 工作履历表实体
 * 对应表：talent_employment
 */
@TableName("talent_employment")
@KeySequence("talent_employment_seq")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalentEmploymentDO extends BaseDO {
    @TableId
    private Long employmentId;
    private Long userId;
    private String company;
    private String position;
    private Date startDate;
    private Date endDate;
    private BigDecimal salary;
    private Boolean deleted;
    private String creator;
    private String updater;
    private Long tenantId;
}