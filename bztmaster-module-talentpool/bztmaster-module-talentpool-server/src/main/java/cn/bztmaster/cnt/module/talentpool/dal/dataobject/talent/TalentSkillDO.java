package cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent;

import lombok.Data;
import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;

/**
 * 技能清单表实体
 * 对应表：talent_skill
 */
@TableName("talent_skill")
@KeySequence("talent_skill_seq")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalentSkillDO extends BaseDO {
    @TableId
    private Long skillId;
    private Long userId;
    private String name;
    private String level;
    private Boolean deleted;
    private String creator;
    private String updater;
    private Long tenantId;
}