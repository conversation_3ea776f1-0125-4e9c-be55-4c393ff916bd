package cn.bztmaster.cnt.module.talentpool.dal.mysql.talent;

import cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentInternshipDO;
import org.apache.ibatis.annotations.Mapper;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Delete;

@Mapper
public interface TalentInternshipMapper extends BaseMapperX<TalentInternshipDO> {
    // 可扩展自定义方法
    @Select("SELECT * FROM talent_internship WHERE user_id = #{userId} AND deleted = 0")
    java.util.List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentInternshipDO> selectListByUserId(
            @Param("userId") Long userId);

    @Delete("DELETE FROM talent_internship WHERE user_id = #{userId}")
    void deleteByUserId(@Param("userId") Long userId);
}