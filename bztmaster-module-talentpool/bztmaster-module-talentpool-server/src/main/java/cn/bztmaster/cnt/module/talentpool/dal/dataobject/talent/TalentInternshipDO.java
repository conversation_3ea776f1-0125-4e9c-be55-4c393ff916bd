package cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent;

import lombok.Data;
import java.util.Date;
import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;

/**
 * 实习经历表实体
 * 对应表：talent_internship
 */
@TableName("talent_internship")
@KeySequence("talent_internship_seq")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalentInternshipDO extends BaseDO {
    @TableId
    private Long internshipId;
    private Long userId;
    private String company;
    private String position;
    private Date startDate;
    private Date endDate;
    private String responsibilities;
    private Boolean deleted;
    private String creator;
    private String updater;
    private Long tenantId;
}