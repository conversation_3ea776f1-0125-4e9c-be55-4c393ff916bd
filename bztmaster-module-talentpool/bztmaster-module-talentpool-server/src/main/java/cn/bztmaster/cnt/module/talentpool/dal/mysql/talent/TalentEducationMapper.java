package cn.bztmaster.cnt.module.talentpool.dal.mysql.talent;

import cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentEducationDO;
import org.apache.ibatis.annotations.Mapper;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Delete;

@Mapper
public interface TalentEducationMapper extends BaseMapperX<TalentEducationDO> {
    // 可扩展自定义方法
    @Select("SELECT * FROM talent_education WHERE user_id = #{userId} AND deleted = 0")
    java.util.List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentEducationDO> selectListByUserId(
            @Param("userId") Long userId);

    @Delete("DELETE FROM talent_education WHERE user_id = #{userId}")
    void deleteByUserId(@Param("userId") Long userId);
}