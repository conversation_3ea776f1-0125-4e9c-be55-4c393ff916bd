package cn.bztmaster.cnt.module.talentpool.enums.talent;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 人才用户状态枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum TalentUserStatusEnum {

    NORMAL("normal", "正常"),
    TO_MERGE("to_merge", "待合并"),
    DISABLED("disabled", "已禁用");

    /**
     * 状态值
     */
    private final String value;
    /**
     * 状态标签
     */
    private final String label;

} 