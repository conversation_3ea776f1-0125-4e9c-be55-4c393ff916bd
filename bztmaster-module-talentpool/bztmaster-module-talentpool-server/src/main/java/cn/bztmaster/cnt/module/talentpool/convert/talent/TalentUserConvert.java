package cn.bztmaster.cnt.module.talentpool.convert.talent;

import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserRespDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserUpdateReqDTO;
import cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import java.util.List;
import java.text.SimpleDateFormat;
import cn.bztmaster.cnt.module.talentpool.convert.talent.DateFormatUtil;

/**
 * 人才用户 Convert
 *
 * 用于DO、DTO、VO之间的对象转换。
 *
 * <AUTHOR>
 */
@Mapper(uses = DateFormatUtil.class)
public interface TalentUserConvert {
        TalentUserConvert INSTANCE = Mappers.getMapper(TalentUserConvert.class);

        @Mappings({
                        @Mapping(source = "userId", target = "id"),
                        @Mapping(source = "identityId", target = "identityId"),
                        @Mapping(source = "birthDate", target = "birthDate", qualifiedByName = "dateToString"),
                        @Mapping(source = "orgId", target = "orgId"),
                        @Mapping(source = "orgName", target = "orgName"),
                        @Mapping(source = "talentSource", target = "talentSource"),
                        @Mapping(source = "isSelfSupport", target = "isSelfSupport")
        })
        TalentUserRespDTO convert(TalentUserDO userDO);

        List<TalentUserRespDTO> convertList(List<TalentUserDO> userDOList);

        TalentUserDO convert(TalentUserUpdateReqDTO updateReqDTO);

        // 子表转换
        @Mappings({
                        @Mapping(source = "startDate", target = "startDate", qualifiedByName = "dateToString"),
                        @Mapping(source = "endDate", target = "endDate", qualifiedByName = "dateToString")
        })
        List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.EducationItem> convertEducationList(
                        List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentEducationDO> list);

        @Mappings({
                        @Mapping(source = "startDate", target = "startDate", qualifiedByName = "dateToString"),
                        @Mapping(source = "endDate", target = "endDate", qualifiedByName = "dateToString")
        })
        List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.CampusPracticeItem> convertCampusPracticeList(
                        List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentCampusPracticeDO> list);

        @Mappings({
                        @Mapping(source = "startDate", target = "startDate", qualifiedByName = "dateToString"),
                        @Mapping(source = "endDate", target = "endDate", qualifiedByName = "dateToString")
        })
        List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.InternshipItem> convertInternshipList(
                        List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentInternshipDO> list);

        List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.ProjectItem> convertProjectList(
                        List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentProjectDO> list);

        @Mappings({
                        @Mapping(source = "completeDate", target = "completeDate", qualifiedByName = "dateToString")
        })
        List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TrainingItem> convertTrainingList(
                        List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentTrainingDO> list);

        List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.SkillItem> convertSkillList(
                        List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentSkillDO> list);

        @Mappings({
                        @Mapping(source = "issueDate", target = "issueDate", qualifiedByName = "dateToString"),
                        @Mapping(source = "expiryDate", target = "expiryDate", qualifiedByName = "dateToString"),
                        @Mapping(source = "certificateNo", target = "certificateNo"),
                        @Mapping(source = "certificateImageUrl", target = "certificateImageUrl")
        })
        List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.CertificateItem> convertCertificateList(
                        List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentCertificateDO> list);

        @Mappings({
                        @Mapping(source = "applyDate", target = "applyDate", qualifiedByName = "dateToString")
        })
        List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.JobApplicationItem> convertJobApplicationList(
                        List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentJobApplicationDO> list);

        @Mappings({
                        @Mapping(source = "startDate", target = "startDate", qualifiedByName = "dateToString"),
                        @Mapping(source = "endDate", target = "endDate", qualifiedByName = "dateToString")
        })
        List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.EmploymentItem> convertEmploymentList(
                        List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentEmploymentDO> list);

        List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.UserTagItem> convertUserTagList(
                        List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserTagDO> list);

        // 评价转换
        cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.CommentItem convertComment(
                        cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserCommentDO commentDO);

        java.util.List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.CommentItem> convertCommentList(
                        java.util.List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserCommentDO> list);
}