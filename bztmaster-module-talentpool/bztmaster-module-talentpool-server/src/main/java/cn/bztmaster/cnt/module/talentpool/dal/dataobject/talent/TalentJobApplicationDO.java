package cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent;

import lombok.Data;
import java.util.Date;
import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;

/**
 * 求职记录表实体
 * 对应表：talent_job_application
 */
@TableName("talent_job_application")
@KeySequence("talent_job_application_seq")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalentJobApplicationDO extends BaseDO {
    @TableId
    private Long applicationId;
    private Long userId;
    private String company;
    private String position;
    private Date applyDate;
    private String status;
    private Boolean deleted;
    private String creator;
    private String updater;
    private Long tenantId;
}