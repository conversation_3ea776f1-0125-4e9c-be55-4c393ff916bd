package cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent;

import lombok.Data;
import java.util.Date;
import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;

/**
 * 校园实践表实体
 * 对应表：talent_campus_practice
 */
@TableName("talent_campus_practice")
@KeySequence("talent_campus_practice_seq")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalentCampusPracticeDO extends BaseDO {
    @TableId
    private Long practiceId;
    private Long userId;
    private String practiceName;
    private String organizer;
    private Date startDate;
    private Date endDate;
    private String practiceReport;
    private Boolean deleted;
    private String creator;
    private String updater;
    private Long tenantId;
}