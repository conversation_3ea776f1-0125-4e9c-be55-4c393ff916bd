package cn.bztmaster.cnt.module.talentpool.service;

import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserPageReqDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserUpdateReqDTO;
import cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserDO;
import cn.bztmaster.cnt.module.talentpool.dal.mysql.talent.TalentUserMapper;
import cn.bztmaster.cnt.module.talentpool.service.talent.TalentUserServiceImpl;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Collections;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TalentUserServiceImpl 的单元测试类
 *
 * <AUTHOR>
 */
public class TalentUserServiceImplTest {

    @InjectMocks
    private TalentUserServiceImpl talentUserService;
    @Mock
    private TalentUserMapper talentUserMapper;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetTalentUserPage() {
        TalentUserPageReqDTO reqDTO = new TalentUserPageReqDTO();
        when(talentUserMapper.selectPage(any(), any(), any(), any(), anyInt(), anyInt()))
                .thenReturn(Collections.emptyList());
        when(talentUserMapper.countPage(any(), any(), any(), any())).thenReturn(0);
        PageResult<?> result = talentUserService.getTalentUserPage(reqDTO);
        assertThat(result.getList()).isEmpty();
        assertThat(result.getTotal()).isEqualTo(0);
    }

    @Test
    public void testGetTalentUser() {
        TalentUserDO userDO = new TalentUserDO();
        userDO.setUserId(1L);
        when(talentUserMapper.selectById(1L)).thenReturn(userDO);
        assertThat(talentUserService.getTalentUser(1L).getId()).isEqualTo(1L);
    }

    @Test
    public void testUpdateTalentUser() {
        TalentUserUpdateReqDTO reqDTO = new TalentUserUpdateReqDTO();
        reqDTO.setId(1L);
        reqDTO.setName("张三");
        reqDTO.setPhone("138****1234");
        reqDTO.setIdentityId("110101199001011234");
        when(talentUserMapper.update(any())).thenReturn(1);
        talentUserService.updateTalentUser(reqDTO);
        verify(talentUserMapper, times(1)).update(any());
    }

    @Test
    public void testDisableTalentUser() {
        when(talentUserMapper.disable(1L)).thenReturn(1);
        talentUserService.disableTalentUser(1L);
        verify(talentUserMapper, times(1)).disable(1L);
    }

    @Test
    public void testMergeTalentUser_identityId() {
        TalentUserDO current = new TalentUserDO();
        current.setUserId(2L);
        current.setIdentityId("ID123");
        TalentUserDO main = new TalentUserDO();
        main.setUserId(1L);
        main.setIdentityId("ID123");
        when(talentUserMapper.selectById(2L)).thenReturn(current);
        when(talentUserMapper.selectByIdentityId("ID123")).thenReturn(main);
        when(talentUserMapper.update(any())).thenReturn(1);
        talentUserService.mergeTalentUser(2L);
        verify(talentUserMapper, times(1)).update(any());
    }

    @Test
    public void testMergeTalentUser_phone() {
        TalentUserDO current = new TalentUserDO();
        current.setUserId(2L);
        current.setPhone("138****1234");
        TalentUserDO main = new TalentUserDO();
        main.setUserId(1L);
        main.setPhone("138****1234");
        when(talentUserMapper.selectById(2L)).thenReturn(current);
        when(talentUserMapper.selectByPhone("138****1234")).thenReturn(main);
        when(talentUserMapper.update(any())).thenReturn(1);
        talentUserService.mergeTalentUser(2L);
        verify(talentUserMapper, times(1)).update(any());
    }
}