package cn.bztmaster.cnt.module.system.api.notify;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.system.api.notify.dto.AppNotifyMessagePageReqDTO;
import cn.bztmaster.cnt.module.system.api.notify.dto.AppNotifyMessageRespDTO;
import cn.bztmaster.cnt.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 小程序站内信 API 接口
 *
 * 提供小程序用户访问站内信的功能
 *
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "用户 App - 站内信")
public interface AppNotifyMessageApi {

    String PREFIX = ApiConstants.PREFIX + "/notify-message";

    @PostMapping(PREFIX + "/page")
    @Operation(summary = "获得我的站内信分页")
    CommonResult<PageResult<AppNotifyMessageRespDTO>> getMyNotifyMessagePage(@RequestBody @Valid AppNotifyMessagePageReqDTO pageReqDTO);

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "获得我的站内信详情")
    @Parameter(name = "id", description = "站内信编号", required = true)
    @Parameter(name = "oneId", description = "用户唯一标识ID", required = true)
    CommonResult<AppNotifyMessageRespDTO> getMyNotifyMessage(@RequestParam("id") Long id, @RequestParam("oneId") String oneId);

    @PostMapping(PREFIX + "/update-read")
    @Operation(summary = "标记站内信为已读")
    @Parameter(name = "id", description = "站内信编号", required = true)
    @Parameter(name = "oneId", description = "用户唯一标识ID", required = true)
    CommonResult<Boolean> updateNotifyMessageRead(@RequestParam("id") Long id, @RequestParam("oneId") String oneId);

    @PostMapping(PREFIX + "/update-all-read")
    @Operation(summary = "标记所有站内信为已读")
    @Parameter(name = "oneId", description = "用户唯一标识ID", required = true)
    CommonResult<Boolean> updateAllNotifyMessageRead(@RequestParam("oneId") String oneId);

    @GetMapping(PREFIX + "/unread-list")
    @Operation(summary = "获得我的未读站内信列表")
    @Parameter(name = "oneId", description = "用户唯一标识ID", required = true)
    CommonResult<List<AppNotifyMessageRespDTO>> getUnreadNotifyMessageList(@RequestParam("oneId") String oneId);

    @GetMapping(PREFIX + "/unread-count")
    @Operation(summary = "获得我的未读站内信数量")
    @Parameter(name = "oneId", description = "用户唯一标识ID", required = true)
    CommonResult<Long> getUnreadNotifyMessageCount(@RequestParam("oneId") String oneId);
}
