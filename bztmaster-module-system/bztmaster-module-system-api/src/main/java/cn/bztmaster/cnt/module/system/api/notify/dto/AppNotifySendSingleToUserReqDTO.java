package cn.bztmaster.cnt.module.system.api.notify.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 用户 App - 站内信发送给用户 Request DTO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 App - 站内信发送给用户 Request DTO")
@Data
public class AppNotifySendSingleToUserReqDTO {

    @Schema(description = "用户唯一标识ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "wx_123456789")
    @NotNull(message = "用户唯一标识ID不能为空")
    private String oneId;

    @Schema(description = "站内信模板编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "USER_SEND")
    @NotEmpty(message = "站内信模板编号不能为空")
    private String templateCode;

    @Schema(description = "邮件模板参数")
    private Map<String, Object> templateParams;
} 