package cn.bztmaster.cnt.module.system.api.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 机构用户信息响应 DTO
 *
 * <AUTHOR>
 */
@Schema(description = "RPC 服务 - 机构用户信息 Response DTO")
@Data
public class AgencyUserInfoRespDTO {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long userId;

    @Schema(description = "账户类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer accountType;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "admin")
    private String username;

    @Schema(description = "昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张先生")
    private String nickname;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "138****5678")
    private String mobile;

    @Schema(description = "头像", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "职位", example = "店长")
    private String position;

    @Schema(description = "部门", example = "运营部")
    private String department;

    @Schema(description = "机构ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long agencyId;

    @Schema(description = "机构名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "传能家政")
    private String agencyName;

    @Schema(description = "合作伙伴ID", example = "1")
    private Long partnerId;

    @Schema(description = "合作伙伴名称", example = "合作伙伴")
    private String partnerName;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer status;

    @Schema(description = "工作状态类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer workStatusType;

    @Schema(description = "权限列表", example = "[\"order_manage\", \"staff_manage\"]")
    private List<String> permissions;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

}
