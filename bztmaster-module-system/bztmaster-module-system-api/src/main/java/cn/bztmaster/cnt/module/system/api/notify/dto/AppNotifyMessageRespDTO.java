package cn.bztmaster.cnt.module.system.api.notify.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 用户 App - 站内信 Response DTO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 App - 站内信 Response DTO")
@Data
public class AppNotifyMessageRespDTO {

    @Schema(description = "站内信编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "用户唯一标识ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "wx_123456789")
    private String oneId;

    @Schema(description = "站内信模板编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "USER_SEND")
    private String templateCode;

    @Schema(description = "站内信模板类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer templateType;

    @Schema(description = "站内信模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "用户发送")
    private String templateNickname;

    @Schema(description = "站内信内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三，欢迎使用站内信功能")
    private String templateContent;

    @Schema(description = "站内信参数", example = "{\"name\": \"张三\", \"content\": \"欢迎使用站内信功能\"}")
    private Map<String, Object> templateParams;

    @Schema(description = "是否已读", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    private Boolean readStatus;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
} 
