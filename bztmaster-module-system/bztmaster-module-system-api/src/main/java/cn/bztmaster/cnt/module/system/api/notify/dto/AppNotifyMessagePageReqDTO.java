package cn.bztmaster.cnt.module.system.api.notify.dto;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

import static cn.bztmaster.cnt.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 用户 App - 站内信分页 Request DTO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 App - 站内信分页 Request DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppNotifyMessagePageReqDTO extends PageParam {

    @Schema(description = "用户唯一标识ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "wx_123456789")
    @NotBlank(message = "用户唯一标识ID不能为空")
    private String oneId;

    @Schema(description = "是否已读", example = "true")
    private Boolean readStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
} 