package cn.bztmaster.cnt.module.system.api.notify;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.system.api.notify.dto.AppNotifySendSingleToUserReqDTO;
import cn.bztmaster.cnt.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 小程序站内信发送 API 接口
 *
 * 提供小程序用户发送站内信的功能
 *
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "用户 App - 站内信发送")
public interface AppNotifySendApi {

    String PREFIX = ApiConstants.PREFIX + "/notify/send";

    @PostMapping(PREFIX + "/send-single")
    @Operation(summary = "发送单条站内信给用户")
    CommonResult<Long> sendSingleMessageToUser(@RequestBody @Valid AppNotifySendSingleToUserReqDTO reqDTO);
}
