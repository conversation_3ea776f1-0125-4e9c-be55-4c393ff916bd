package cn.bztmaster.cnt.module.system.api.app;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.system.api.app.dto.AgencyUserInfoRespDTO;
import cn.bztmaster.cnt.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 机构端用户 API 接口
 *
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 机构端用户")
public interface AppUserApi {

    String PREFIX = ApiConstants.PREFIX + "/app-user";

    @GetMapping(PREFIX + "/get-by-mobile")
    @Operation(summary = "根据手机号获取机构用户信息")
    @Parameter(name = "mobile", description = "手机号码", required = true, example = "13812345678")
    CommonResult<AgencyUserInfoRespDTO> getAgencyUserByMobile(@RequestParam("mobile") String mobile);

}
