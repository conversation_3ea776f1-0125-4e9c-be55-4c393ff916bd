package cn.bztmaster.cnt.module.system.api.app;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.system.api.app.dto.AgencyUserInfoRespDTO;
import cn.bztmaster.cnt.module.system.controller.app.user.vo.AgencyUserInfoRespVO;
import cn.bztmaster.cnt.module.system.controller.app.user.AppUserController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;
import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.error;

/**
 * 机构端用户 API 实现类
 *
 * <AUTHOR>
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class AppUserApiImpl implements AppUserApi {

    @Resource
    private AppUserController appUserController;

    @Override
    public CommonResult<AgencyUserInfoRespDTO> getAgencyUserByMobile(String mobile) {
        // 调用现有的Controller方法
        CommonResult<AgencyUserInfoRespVO> result = appUserController.getAgencyUserByMobile(mobile);

        if (result.isSuccess() && result.getData() != null) {
            // 转换为DTO
            AgencyUserInfoRespDTO dto = convertToDTO(result.getData());
            return success(dto);
        }

        return error(result.getCode(), result.getMsg());
    }

    /**
     * 将VO转换为DTO
     */
    private AgencyUserInfoRespDTO convertToDTO(AgencyUserInfoRespVO vo) {
        AgencyUserInfoRespDTO dto = new AgencyUserInfoRespDTO();
        dto.setUserId(vo.getUserId());
        dto.setAccountType(vo.getAccountType());
        dto.setUsername(vo.getUsername());
        dto.setNickname(vo.getNickname());
        dto.setMobile(vo.getMobile());
        dto.setAvatar(vo.getAvatar());
        dto.setPosition(vo.getPosition());
        dto.setDepartment(vo.getDepartment());
        dto.setAgencyId(vo.getAgencyId());
        dto.setAgencyName(vo.getAgencyName());
        dto.setPartnerId(vo.getPartnerId());
        dto.setPartnerName(vo.getPartnerName());
        dto.setStatus(vo.getStatus());
        dto.setWorkStatusType(vo.getWorkStatusType());
        dto.setPermissions(vo.getPermissions());
        dto.setLastLoginTime(vo.getLastLoginTime());
        return dto;
    }

}
