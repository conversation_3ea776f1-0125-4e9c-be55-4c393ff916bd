# 合作伙伴资质文件集成实现文档

## 实现概述

已成功在就业服务机构数据同步功能中集成了批量更新资质文件的逻辑，实现了合作伙伴管理与机构资质文件管理的无缝集成。

## 修改的文件

### 1. VO类修改

#### PartnerSaveReqVO.java
- **新增字段**：`AgencyQualificationBatchUpdateReqVO qualificationFiles`
- **用途**：在新增合作伙伴时传递资质文件信息

#### PartnerUpdateReqVO.java  
- **新增字段**：`AgencyQualificationBatchUpdateReqVO qualificationFiles`
- **用途**：在编辑合作伙伴时传递资质文件信息

### 2. Service层修改

#### PartnerServiceImpl.java

**新增依赖**：
```java
@Resource
private AgencyQualificationService agencyQualificationService;
```

**修改的方法**：

1. **syncAgencyData()** - 同步机构数据方法
   - 新增参数：`AgencyQualificationBatchUpdateReqVO qualificationFiles`
   - 在方法末尾调用资质文件处理逻辑

2. **createPartner()** - 新增合作伙伴方法
   - 传递 `reqVO.getQualificationFiles()` 到 `syncAgencyData()`

3. **updatePartner()** - 更新合作伙伴方法
   - 传递 `reqVO.getQualificationFiles()` 到 `syncAgencyData()`

**新增方法**：

4. **handleQualificationFiles()** - 处理资质文件批量更新
   - 验证参数有效性
   - 确保所有文件的agencyId正确设置
   - 调用批量更新资质文件服务
   - 异常向上传播（确保事务回滚）

## 核心业务逻辑

### 1. 数据同步流程

```
合作伙伴操作 → 机构数据同步 → 资质文件批量更新
     ↓              ↓                ↓
  新增/编辑    →  创建/更新机构  →  更新资质文件
```

### 2. 资质文件处理逻辑

1. **参数验证**：检查机构ID和资质文件信息是否有效
2. **ID校正**：确保所有文件的agencyId与当前机构ID一致
3. **批量更新**：调用 `agencyQualificationService.batchUpdateQualifications()`
4. **异常传播**：资质文件更新失败时异常向上传播，触发事务回滚

### 3. 事务处理

- 合作伙伴信息更新和资质文件更新在同一个事务中执行
- 使用 `@Transactional(rollbackFor = Exception.class)` 确保数据一致性
- 资质文件更新异常会触发整个事务回滚，确保数据完整性

## 使用示例

### 新增合作伙伴时同步资质文件

```json
{
  "name": "测试家政机构",
  "type": "家政机构",
  "creditCode": "91110000123456789X",
  "qualificationFiles": {
    "files": [
      {
        "agencyId": null,
        "fileType": "business_license",
        "fileCategory": "business_license",
        "fileDescription": "营业执照扫描件",
        "fileName": "营业执照.jpg",
        "fileUrl": "https://example.com/files/business_license.jpg",
        "fileSize": 512000,
        "fileExtension": "jpg",
        "sortOrder": 1
      },
      {
        "agencyId": null,
        "fileType": "door_photo",
        "fileCategory": "door_photo",
        "fileDescription": "门头照片",
        "fileName": "门头照.jpg",
        "fileUrl": "https://example.com/files/door_photo.jpg",
        "fileSize": 350000,
        "fileExtension": "jpg",
        "sortOrder": 2
      }
    ]
  }
}
```

### 编辑合作伙伴时更新资质文件

```json
{
  "id": 1001,
  "name": "更新后的家政机构",
  "type": "家政机构",
  "creditCode": "91110000123456789X",
  "qualificationFiles": {
    "files": [
      {
        "agencyId": 2001,
        "fileType": "business_license",
        "fileCategory": "business_license",
        "fileDescription": "更新的营业执照",
        "fileName": "营业执照_新版.jpg",
        "fileUrl": "https://example.com/files/business_license_new.jpg",
        "fileSize": 620000,
        "fileExtension": "jpg",
        "sortOrder": 1
      }
    ]
  }
}
```

## 特殊处理逻辑

### 1. 机构ID自动设置
- 如果资质文件中的agencyId为null，自动设置为当前机构ID
- 如果agencyId与当前机构ID不一致，自动修正为当前机构ID

### 2. 异常处理策略
- 资质文件更新失败时异常向上传播
- 触发整个事务回滚，确保数据一致性
- 合作伙伴信息、机构数据和资质文件要么全部成功，要么全部失败

### 3. 机构类型限制
- 只有当合作伙伴类型为"家政机构"时才会同步到就业服务机构
- 其他类型的合作伙伴不会触发机构数据同步和资质文件更新

## 测试建议

### 1. 功能测试
- 测试新增家政机构类型合作伙伴时的资质文件同步
- 测试编辑家政机构类型合作伙伴时的资质文件更新
- 测试非家政机构类型合作伙伴不触发资质文件处理

### 2. 异常测试
- 测试资质文件信息为空的情况
- 测试资质文件更新失败时的事务回滚
- 测试agencyId不一致时的自动修正

### 3. 事务测试
- 测试资质文件更新失败时整个事务的回滚
- 验证合作伙伴信息、机构数据和资质文件的事务一致性
- 确认失败后所有数据都不会被保存

## 注意事项

1. **性能考虑**：批量更新资质文件可能涉及多个数据库操作，建议监控性能
2. **事务管理**：资质文件更新失败会导致整个操作回滚，需要考虑用户体验
3. **权限验证**：确保调用资质文件更新服务时有适当的权限
4. **数据验证**：建议在前端和后端都进行资质文件数据的有效性验证
5. **错误提示**：需要向用户提供清晰的错误信息，说明哪个环节失败了

## 扩展建议

1. **异步处理**：对于大量资质文件的更新，可以考虑异步处理
2. **批量优化**：可以进一步优化批量更新的性能
3. **审计日志**：可以添加资质文件变更的审计日志
4. **回滚机制**：可以考虑添加资质文件更新的回滚机制
