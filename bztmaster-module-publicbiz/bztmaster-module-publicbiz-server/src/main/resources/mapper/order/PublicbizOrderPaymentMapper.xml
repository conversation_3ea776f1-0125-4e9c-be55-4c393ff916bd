<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderPaymentMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderPaymentDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="payment_no" property="paymentNo" jdbcType="VARCHAR"/>
        <result column="payment_type" property="paymentType" jdbcType="VARCHAR"/>
        <result column="payment_amount" property="paymentAmount" jdbcType="DECIMAL"/>
        <result column="payment_status" property="paymentStatus" jdbcType="VARCHAR"/>
        <result column="payment_time" property="paymentTime" jdbcType="TIMESTAMP"/>
        <result column="operator_id" property="operatorId" jdbcType="BIGINT"/>
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR"/>
        <result column="payment_remark" property="paymentRemark" jdbcType="VARCHAR"/>
        <result column="transaction_id" property="transactionId" jdbcType="VARCHAR"/>
        <result column="is_escrow" property="isEscrow" jdbcType="BOOLEAN"/>
        <result column="platform_payment_no" property="platformPaymentNo" jdbcType="VARCHAR"/>
        <result column="platform_payment_status" property="platformPaymentStatus" jdbcType="VARCHAR"/>
        <result column="platform_payment_time" property="platformPaymentTime" jdbcType="TIMESTAMP"/>
        <result column="receiver_id" property="receiverId" jdbcType="BIGINT"/>
        <result column="notify_status" property="notifyStatus" jdbcType="VARCHAR"/>
        <result column="notify_time" property="notifyTime" jdbcType="TIMESTAMP"/>
        <result column="channel_type" property="channelType" jdbcType="VARCHAR"/>
        <result column="bank_code" property="bankCode" jdbcType="VARCHAR"/>
        <result column="payer_account" property="payerAccount" jdbcType="VARCHAR"/>
        <result column="payment_provider" property="paymentProvider" jdbcType="VARCHAR"/>
        <result column="third_party_sub_merchant_id" property="thirdPartySubMerchantId" jdbcType="VARCHAR"/>
        <result column="tpp_code" property="tppCode" jdbcType="VARCHAR"/>
        <result column="third_party_split_batch_no" property="thirdPartySplitBatchNo" jdbcType="VARCHAR"/>
        <result column="split_amount" property="splitAmount" jdbcType="DECIMAL"/>
        <result column="platform_fee" property="platformFee" jdbcType="DECIMAL"/>
        <result column="split_status" property="splitStatus" jdbcType="VARCHAR"/>
        <result column="split_fail_reason" property="splitFailReason" jdbcType="VARCHAR"/>
        <result column="split_party_type" property="splitPartyType" jdbcType="VARCHAR"/>
        <result column="refund_no" property="refundNo" jdbcType="VARCHAR"/>
        <result column="refund_amount" property="refundAmount" jdbcType="DECIMAL"/>
        <result column="refund_status" property="refundStatus" jdbcType="VARCHAR"/>
        <result column="refund_time" property="refundTime" jdbcType="TIMESTAMP"/>
        <result column="third_party_refund_id" property="thirdPartyRefundId" jdbcType="VARCHAR"/>
        <result column="refund_reason" property="refundReason" jdbcType="VARCHAR"/>
        <result column="notify_content" property="notifyContent" jdbcType="LONGVARCHAR"/>
        <result column="agency_id" property="agencyId" jdbcType="BIGINT"/>
        <result column="agency_name" property="agencyName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="BOOLEAN"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, order_id, order_no, payment_no, payment_type, payment_amount, payment_status, payment_time,
        operator_id, operator_name, payment_remark, transaction_id, is_escrow, platform_payment_no,
        platform_payment_status, platform_payment_time, receiver_id, notify_status, notify_time,
        channel_type, bank_code, payer_account, payment_provider, third_party_sub_merchant_id, tpp_code,
        third_party_split_batch_no, split_amount, platform_fee, split_status, split_fail_reason,
        split_party_type, refund_no, refund_amount, refund_status, refund_time, third_party_refund_id,
        refund_reason, notify_content, agency_id, agency_name, create_time, update_time, creator, updater,
        deleted, tenant_id
    </sql>

    <!-- 根据机构ID和支付状态统计支付金额 -->
    <select id="selectPaymentAmountByAgencyIdAndStatus" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(payment_amount), 0)
        FROM publicbiz_order_payment
        WHERE agency_id = #{agencyId}
          AND payment_status = #{paymentStatus}
          AND deleted = 0
    </select>

    <!-- 根据机构ID查询支付统计信息 -->
    <select id="selectPaymentStatsByAgencyId" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_count,
            COUNT(CASE WHEN payment_status = 'success' THEN 1 END) as success_count,
            COUNT(CASE WHEN payment_status = 'failed' THEN 1 END) as failed_count,
            COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_count,
            COALESCE(SUM(CASE WHEN payment_status = 'success' THEN payment_amount ELSE 0 END), 0) as total_amount,
            COALESCE(SUM(CASE WHEN payment_status = 'success' THEN platform_fee ELSE 0 END), 0) as total_platform_fee
        FROM publicbiz_order_payment
        WHERE agency_id = #{agencyId}
          AND deleted = 0
    </select>

    <!-- 根据机构ID和时间范围查询支付记录 -->
    <select id="selectByAgencyIdAndTimeRange" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM publicbiz_order_payment
        WHERE agency_id = #{agencyId}
          AND payment_time BETWEEN #{startTime} AND #{endTime}
          AND deleted = 0
        ORDER BY payment_time DESC
    </select>

    <!-- 根据机构名称模糊查询支付记录 -->
    <select id="selectByAgencyNameLike" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM publicbiz_order_payment
        WHERE agency_name LIKE CONCAT('%', #{agencyName}, '%')
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据机构ID和分账状态查询支付记录 -->
    <select id="selectByAgencyIdAndSplitStatus" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM publicbiz_order_payment
        WHERE agency_id = #{agencyId}
          AND split_status = #{splitStatus}
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据机构ID和退款状态查询支付记录 -->
    <select id="selectByAgencyIdAndRefundStatus" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM publicbiz_order_payment
        WHERE agency_id = #{agencyId}
          AND refund_status = #{refundStatus}
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

</mapper>
