package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 切换身份响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "切换身份响应 VO")
@Data
public class SwitchIdentityRespVO {

    @Schema(description = "记录ID", example = "1024")
    private Long id;

    @Schema(description = "身份类型：AGENCY-机构，EMPLOYER-雇主，AUNT-阿姨", requiredMode = Schema.RequiredMode.REQUIRED, example = "EMPLOYER")
    private String identityType;

    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    @Schema(description = "刷新令牌", example = "refresh_token_123456")
    private String refreshToken;

    @Schema(description = "机构用户信息（当identityType为AGENCY时返回）")
    private GetUserCacheInfoRespVO.AgencyUserInfo agencyUserInfo;

    @Schema(description = "雇主用户信息（当identityType为EMPLOYER时返回）")
    private GetUserCacheInfoRespVO.EmployerUserInfo employerUserInfo;

    @Schema(description = "阿姨用户信息（当identityType为AUNT时返回）")
    private GetUserCacheInfoRespVO.AuntUserInfo auntUserInfo;
}
