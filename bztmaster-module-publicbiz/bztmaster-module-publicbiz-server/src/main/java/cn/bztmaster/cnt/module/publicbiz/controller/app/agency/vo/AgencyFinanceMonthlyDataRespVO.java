package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 机构财务月度数据 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构端 - 机构财务月度数据 Response VO")
@Data
public class AgencyFinanceMonthlyDataRespVO {

    @Schema(description = "月度汇总数据")
    private MonthlySummary summary;

    @Schema(description = "交易明细列表")
    private TransactionList transactions;

    @Schema(description = "月度汇总数据")
    @Data
    public static class MonthlySummary {

        @Schema(description = "年份", example = "2025")
        private Integer year;

        @Schema(description = "月份", example = "7")
        private Integer month;

        @Schema(description = "收入总金额", example = "10000.00")
        private String incomeAmount;

        @Schema(description = "支出总金额", example = "5800.00")
        private String expenseAmount;

        @Schema(description = "净收入", example = "4200.00")
        private String netAmount;

    }

    @Schema(description = "交易明细列表")
    @Data
    public static class TransactionList {

        @Schema(description = "交易记录列表")
        private List<Transaction> list;

        @Schema(description = "总记录数", example = "100")
        private Long total;

        @Schema(description = "当前页码", example = "1")
        private Integer pageNo;

        @Schema(description = "每页数量", example = "20")
        private Integer pageSize;

        @Schema(description = "总页数", example = "5")
        private Integer totalPages;

    }

    @Schema(description = "交易记录")
    @Data
    public static class Transaction {

        @Schema(description = "交易记录ID", example = "123")
        private String id;

        @Schema(description = "交易描述", example = "支出保险订单(在线支付)")
        private String description;

        @Schema(description = "交易时间", example = "2025-07-10 14:09:25")
        private String time;

        @Schema(description = "交易金额", example = "-2300.00")
        private String amount;

        @Schema(description = "交易类型", example = "expense")
        private String type;

        @Schema(description = "账户结余信息", example = "在线支付结余: ¥360")
        private String balance;

        @Schema(description = "关联订单号", example = "DD202507100001")
        private String orderNo;

        @Schema(description = "支付方式", example = "online")
        private String paymentMethod;

        @Schema(description = "交易分类", example = "insurance")
        private String category;

        @Schema(description = "交易状态", example = "success")
        private String status;

    }

}
