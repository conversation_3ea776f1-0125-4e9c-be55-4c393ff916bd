package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 机构资质文件批量更新请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构资质文件批量更新请求 VO")
@Data
public class AgencyQualificationBatchUpdateReqVO {

    @Schema(description = "文件对象数组", required = true)
    @NotEmpty(message = "文件列表不能为空")
    @Valid
    private List<FileInfo> files;

    @Schema(description = "文件信息")
    @Data
    public static class FileInfo {

        @Schema(description = "机构ID", example = "1001", required = true)
        @NotNull(message = "机构ID不能为空")
        private Long agencyId;

        @Schema(description = "文件类型", example = "business_license", required = true)
        @NotNull(message = "文件类型不能为空")
        private String fileType;

        @Schema(description = "文件分类", example = "business_license")
        private String fileCategory;

        @Schema(description = "文件描述", example = "营业执照扫描件")
        private String fileDescription;

        @Schema(description = "文件名", example = "营业执照更新版.jpg", required = true)
        @NotNull(message = "文件名不能为空")
        private String fileName;

        @Schema(description = "文件URL", example = "https://example.com/files/a/business_license_new.jpg", required = true)
        @NotNull(message = "文件URL不能为空")
        private String fileUrl;

        @Schema(description = "文件大小（单位：字节）", example = "620000")
        private Long fileSize;

        @Schema(description = "文件扩展名", example = "jpg")
        private String fileExtension;

        @Schema(description = "排序序号（数字越小越靠前）", example = "1")
        private Integer sortOrder;
    }
}
