package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 机构资质文件列表响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构资质文件列表响应 VO")
@Data
public class AgencyQualificationListRespVO {

    @Schema(description = "机构ID", example = "1001")
    private Long agencyId;

    @Schema(description = "文件类型", example = "business_license")
    private String fileType;

    @Schema(description = "文件分类", example = "business_license")
    private String fileCategory;

    @Schema(description = "文件描述", example = "营业执照扫描件")
    private String fileDescription;

    @Schema(description = "文件名", example = "营业执照A.jpg")
    private String fileName;

    @Schema(description = "文件URL", example = "https://example.com/files/a/business_license.jpg")
    private String fileUrl;

    @Schema(description = "文件大小（单位：字节）", example = "512000")
    private Long fileSize;

    @Schema(description = "文件扩展名", example = "jpg")
    private String fileExtension;

    @Schema(description = "排序序号（数字越小越靠前）", example = "1")
    private Integer sortOrder;
}
