package cn.bztmaster.cnt.module.publicbiz.service.employer.impl;

import cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.enums.UserTypeEnum;
import cn.bztmaster.cnt.framework.common.biz.system.oauth2.OAuth2TokenCommonApi;
import cn.bztmaster.cnt.framework.common.biz.system.oauth2.dto.OAuth2AccessTokenCreateReqDTO;
import cn.bztmaster.cnt.framework.common.biz.system.oauth2.dto.OAuth2AccessTokenRespDTO;
import cn.bztmaster.cnt.module.system.enums.oauth2.OAuth2ClientConstants;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.MpUserLastLoginIdentityRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.MpUserLastLoginIdentitySaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.GetUserCacheInfoRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.SwitchIdentityRespVO;
import cn.bztmaster.cnt.module.publicbiz.convert.employer.MpUserLastLoginIdentityConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserLastLoginIdentityDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.MpUserLastLoginIdentityMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.MpUserMapper;
import cn.bztmaster.cnt.module.publicbiz.service.employer.MpUserLastLoginIdentityService;
import cn.bztmaster.cnt.module.system.api.app.AppUserApi;
import cn.bztmaster.cnt.module.system.api.app.dto.AgencyUserInfoRespDTO;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.MP_USER_LAST_LOGIN_IDENTITY_NOT_EXISTS;

/**
 * 小程序用户最后登录身份记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MpUserLastLoginIdentityServiceImpl implements MpUserLastLoginIdentityService {

    @Resource
    private MpUserLastLoginIdentityMapper mpUserLastLoginIdentityMapper;

    @Resource
    private MpUserMapper mpUserMapper;

    @Resource
    private AppUserApi appUserApi;

    @Resource
    private OAuth2TokenCommonApi oauth2TokenApi;

    @Override
    public Long createMpUserLastLoginIdentity(MpUserLastLoginIdentitySaveReqVO createReqVO) {
        // 插入
        MpUserLastLoginIdentityDO mpUserLastLoginIdentity = MpUserLastLoginIdentityConvert.INSTANCE
                .convert(createReqVO);
        mpUserLastLoginIdentityMapper.insert(mpUserLastLoginIdentity);
        // 返回
        return mpUserLastLoginIdentity.getId();
    }

    @Override
    public void updateMpUserLastLoginIdentity(MpUserLastLoginIdentitySaveReqVO updateReqVO) {
        // 校验存在
        if (updateReqVO.getId() != null) {
            validateMpUserLastLoginIdentityExists(updateReqVO.getId());
        }
        // 更新
        MpUserLastLoginIdentityDO updateObj = MpUserLastLoginIdentityConvert.INSTANCE.convert(updateReqVO);
        mpUserLastLoginIdentityMapper.updateById(updateObj);
    }

    @Override
    public void deleteMpUserLastLoginIdentity(Long id) {
        // 校验存在
        validateMpUserLastLoginIdentityExists(id);
        // 删除
        mpUserLastLoginIdentityMapper.deleteById(id);
    }

    private void validateMpUserLastLoginIdentityExists(Long id) {
        if (mpUserLastLoginIdentityMapper.selectById(id) == null) {
            throw ServiceExceptionUtil.exception(MP_USER_LAST_LOGIN_IDENTITY_NOT_EXISTS);
        }
    }

    @Override
    public MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentity(Long id) {
        MpUserLastLoginIdentityDO mpUserLastLoginIdentity = mpUserLastLoginIdentityMapper.selectById(id);
        return MpUserLastLoginIdentityConvert.INSTANCE.convert(mpUserLastLoginIdentity);
    }

    @Override
    public MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentityByUserIdAndOpenid(Long userId, String openid) {
        MpUserLastLoginIdentityDO mpUserLastLoginIdentity = mpUserLastLoginIdentityMapper
                .selectByUserIdAndOpenid(userId, openid);
        return MpUserLastLoginIdentityConvert.INSTANCE.convert(mpUserLastLoginIdentity);
    }

    @Override
    public MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentityByOpenid(String openid) {
        MpUserLastLoginIdentityDO mpUserLastLoginIdentity = mpUserLastLoginIdentityMapper.selectByOpenid(openid);
        return MpUserLastLoginIdentityConvert.INSTANCE.convert(mpUserLastLoginIdentity);
    }

    @Override
    public MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentityByUserId(Long userId) {
        MpUserLastLoginIdentityDO mpUserLastLoginIdentity = mpUserLastLoginIdentityMapper.selectByUserId(userId);
        return MpUserLastLoginIdentityConvert.INSTANCE.convert(mpUserLastLoginIdentity);
    }

    @Override
    public Long saveOrUpdateMpUserLastLoginIdentity(MpUserLastLoginIdentitySaveReqVO saveReqVO) {
        // 先查询是否已存在
        MpUserLastLoginIdentityDO existingRecord = mpUserLastLoginIdentityMapper.selectByUserIdAndOpenid(
                saveReqVO.getUserId(), saveReqVO.getOpenid());

        if (existingRecord != null) {
            // 存在则更新
            saveReqVO.setId(existingRecord.getId());
            MpUserLastLoginIdentityDO updateObj = MpUserLastLoginIdentityConvert.INSTANCE.convert(saveReqVO);
            updateObj.setUpdateTime(LocalDateTime.now());
            mpUserLastLoginIdentityMapper.updateById(updateObj);
            return existingRecord.getId();
        } else {
            // 不存在则创建
            return createMpUserLastLoginIdentity(saveReqVO);
        }
    }

    @Override
    public Long switchIdentityByOpenid(String openid, String identityType) {
        // 根据openid查询现有记录
        MpUserLastLoginIdentityDO existingRecord = mpUserLastLoginIdentityMapper.selectByOpenid(openid);

        if (existingRecord != null) {
            // 存在则更新身份类型和最后登录时间
            existingRecord.setIdentityType(identityType);
            existingRecord.setLastLoginTime(LocalDateTime.now());
            existingRecord.setUpdateTime(LocalDateTime.now());
            mpUserLastLoginIdentityMapper.updateById(existingRecord);
            return existingRecord.getId();
        } else {
            // 不存在则创建新记录
            MpUserLastLoginIdentityDO newRecord = new MpUserLastLoginIdentityDO();
            newRecord.setOpenid(openid);
            newRecord.setIdentityType(identityType);
            newRecord.setLastLoginTime(LocalDateTime.now());
            newRecord.setLoginStatus(1); // 默认启用状态
            mpUserLastLoginIdentityMapper.insert(newRecord);
            return newRecord.getId();
        }
    }

    @Override
    public GetUserCacheInfoRespVO getUserCacheInfoByOpenid(String openid) {
        log.info("根据openid获取用户缓存信息 - openid: {}", openid);

        // 1. 根据openid查询最后登录身份记录
        MpUserLastLoginIdentityDO lastLoginIdentity = mpUserLastLoginIdentityMapper.selectByOpenid(openid);
        if (lastLoginIdentity == null) {
            log.warn("未找到用户最后登录身份记录 - openid: {}", openid);
            return null;
        }

        // 2. 根据身份类型返回不同的用户信息
        GetUserCacheInfoRespVO result = new GetUserCacheInfoRespVO();
        result.setIdentityType(lastLoginIdentity.getIdentityType());

        switch (lastLoginIdentity.getIdentityType()) {
            case "AGENCY":
                // 机构用户：根据openId查找mpUser表的mobile，用手机号去请求system模块的接口
                MpUserDO mpUser = mpUserMapper.selectByOpenid(openid);
                if (mpUser != null && StrUtil.isNotBlank(mpUser.getMobile())) {
                    try {
                        // 调用system模块的API接口获取机构用户信息
                        CommonResult<AgencyUserInfoRespDTO> apiResult = appUserApi
                                .getAgencyUserByMobile(mpUser.getMobile());
                        if (apiResult.isSuccess() && apiResult.getData() != null) {
                            AgencyUserInfoRespDTO agencyUser = apiResult.getData();
                            GetUserCacheInfoRespVO.AgencyUserInfo agencyUserInfo = new GetUserCacheInfoRespVO.AgencyUserInfo();
                            agencyUserInfo.setId(agencyUser.getUserId());
                            agencyUserInfo.setUsername(agencyUser.getUsername());
                            agencyUserInfo.setNickname(agencyUser.getNickname());
                            agencyUserInfo.setMobile(agencyUser.getMobile());
                            agencyUserInfo.setAvatar(agencyUser.getAvatar());
                            agencyUserInfo.setDeptName(agencyUser.getDepartment());
                            agencyUserInfo.setAgencyId(agencyUser.getAgencyId());
                            agencyUserInfo.setAgencyName(agencyUser.getAgencyName());
                            agencyUserInfo.setStatus(agencyUser.getStatus());
                            result.setAgencyUserInfo(agencyUserInfo);

                            // 为机构用户创建Token令牌
                            try {
                                OAuth2AccessTokenRespDTO accessTokenRespDTO = oauth2TokenApi
                                        .createAccessToken(new OAuth2AccessTokenCreateReqDTO()
                                                .setUserId(agencyUser.getUserId())
                                                .setUserType(UserTypeEnum.MEMBER.getValue())
                                                .setClientId(OAuth2ClientConstants.CLIENT_ID_DEFAULT))
                                        .getCheckedData();
                                result.setToken(accessTokenRespDTO.getAccessToken());
                                result.setRefreshToken(accessTokenRespDTO.getRefreshToken());
                                log.info("为机构用户创建Token成功 - userId: {}, token: {}", agencyUser.getUserId(),
                                        accessTokenRespDTO.getAccessToken());
                            } catch (Exception e) {
                                log.error("为机构用户创建Token失败 - userId: {}, error: {}", agencyUser.getUserId(),
                                        e.getMessage(), e);
                            }
                        } else {
                            log.warn("调用system模块API接口失败 - mobile: {}, error: {}", mpUser.getMobile(),
                                    apiResult.getMsg());
                        }
                    } catch (Exception e) {
                        log.error("调用system模块API接口获取机构用户信息失败 - mobile: {}, error: {}", mpUser.getMobile(),
                                e.getMessage(), e);
                    }
                }
                break;

            case "EMPLOYER":
                // 雇主用户：根据openId查找mpUser表，将用户信息返回
                MpUserDO employerUser = mpUserMapper.selectByOpenid(openid);
                if (employerUser != null) {
                    GetUserCacheInfoRespVO.EmployerUserInfo employerUserInfo = new GetUserCacheInfoRespVO.EmployerUserInfo();
                    employerUserInfo.setId(employerUser.getId());
                    employerUserInfo.setOneid(employerUser.getOneid());
                    employerUserInfo.setOpenid(employerUser.getOpenid());
                    employerUserInfo.setNickname(employerUser.getNickname());
                    employerUserInfo.setMobile(employerUser.getMobile());
                    employerUserInfo.setHeadImageUrl(employerUser.getHeadImageUrl());
                    employerUserInfo.setSubscribeStatus(employerUser.getSubscribeStatus());
                    employerUserInfo.setSubscribeTime(
                            employerUser.getSubscribeTime() != null ? employerUser.getSubscribeTime().toString()
                                    : null);
                    employerUserInfo.setLanguage(employerUser.getLanguage());
                    employerUserInfo.setCountry(employerUser.getCountry());
                    employerUserInfo.setProvince(employerUser.getProvince());
                    employerUserInfo.setCity(employerUser.getCity());
                    result.setEmployerUserInfo(employerUserInfo);

                    // 为雇主用户创建Token令牌
                    try {
                        OAuth2AccessTokenRespDTO accessTokenRespDTO = oauth2TokenApi
                                .createAccessToken(new OAuth2AccessTokenCreateReqDTO()
                                        .setUserId(employerUser.getId())
                                        .setUserType(UserTypeEnum.MEMBER.getValue())
                                        .setClientId(OAuth2ClientConstants.CLIENT_ID_DEFAULT))
                                .getCheckedData();
                        result.setToken(accessTokenRespDTO.getAccessToken());
                        result.setRefreshToken(accessTokenRespDTO.getRefreshToken());
                        log.info("为雇主用户创建Token成功 - userId: {}, token: {}", employerUser.getId(),
                                accessTokenRespDTO.getAccessToken());
                    } catch (Exception e) {
                        log.error("为雇主用户创建Token失败 - userId: {}, error: {}", employerUser.getId(), e.getMessage(), e);
                    }
                }
                break;

            case "AUNT":
                // 阿姨用户：根据openId查找mpUser表，返回oneId字段
                MpUserDO auntUser = mpUserMapper.selectByOpenid(openid);
                if (auntUser != null) {
                    GetUserCacheInfoRespVO.AuntUserInfo auntUserInfo = new GetUserCacheInfoRespVO.AuntUserInfo();
                    auntUserInfo.setOneid(auntUser.getOneid());
                    result.setAuntUserInfo(auntUserInfo);

                    // 为阿姨用户创建Token令牌（使用MEMBER类型）
                    try {
                        OAuth2AccessTokenRespDTO accessTokenRespDTO = oauth2TokenApi
                                .createAccessToken(new OAuth2AccessTokenCreateReqDTO()
                                        .setUserId(auntUser.getId())
                                        .setUserType(UserTypeEnum.MEMBER.getValue())
                                        .setClientId(OAuth2ClientConstants.CLIENT_ID_DEFAULT))
                                .getCheckedData();
                        result.setToken(accessTokenRespDTO.getAccessToken());
                        result.setRefreshToken(accessTokenRespDTO.getRefreshToken());
                        log.info("为阿姨用户创建Token成功 - userId: {}, token: {}", auntUser.getId(),
                                accessTokenRespDTO.getAccessToken());
                    } catch (Exception e) {
                        log.error("为阿姨用户创建Token失败 - userId: {}, error: {}", auntUser.getId(), e.getMessage(), e);
                    }
                }
                break;

            default:
                log.warn("未知的身份类型 - identityType: {}", lastLoginIdentity.getIdentityType());
                break;
        }

        log.info("获取用户缓存信息成功 - openid: {}, identityType: {}", openid, lastLoginIdentity.getIdentityType());
        return result;
    }

    @Override
    public SwitchIdentityRespVO switchIdentityAndGetUserInfo(String openid, String identityType) {
        log.info("切换用户身份并获取用户信息 - openid: {}, identityType: {}", openid, identityType);

        // 1. 先切换身份
        Long recordId = switchIdentityByOpenid(openid, identityType);
        log.info("身份切换成功 - recordId: {}", recordId);

        // 2. 获取切换后的用户缓存信息
        GetUserCacheInfoRespVO userCacheInfo = getUserCacheInfoByOpenid(openid);
        if (userCacheInfo == null) {
            log.warn("获取用户缓存信息失败 - openid: {}", openid);
            return null;
        }

        // 3. 构建响应数据
        SwitchIdentityRespVO result = new SwitchIdentityRespVO();
        result.setId(recordId);
        result.setIdentityType(userCacheInfo.getIdentityType());
        result.setToken(userCacheInfo.getToken());
        result.setRefreshToken(userCacheInfo.getRefreshToken());
        result.setAgencyUserInfo(userCacheInfo.getAgencyUserInfo());
        result.setEmployerUserInfo(userCacheInfo.getEmployerUserInfo());
        result.setAuntUserInfo(userCacheInfo.getAuntUserInfo());

        log.info("切换身份并获取用户信息成功 - openid: {}, identityType: {}, recordId: {}",
                openid, identityType, recordId);
        return result;
    }

}
