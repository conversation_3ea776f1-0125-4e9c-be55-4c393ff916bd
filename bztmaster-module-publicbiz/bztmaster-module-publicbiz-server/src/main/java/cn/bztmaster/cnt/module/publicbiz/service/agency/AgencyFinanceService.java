package cn.bztmaster.cnt.module.publicbiz.service.agency;

import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyFinanceBalanceRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyFinanceMonthlyDataReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyFinanceMonthlyDataRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyFinanceStatisticsReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyFinanceStatisticsRespVO;

/**
 * 机构财务中心 Service 接口
 *
 * <AUTHOR>
 */
public interface AgencyFinanceService {

    /**
     * 获取机构累计结余
     *
     * @param agencyId 机构ID
     * @return 结余信息
     */
    AgencyFinanceBalanceRespVO getAgencyBalance(String agencyId);

    /**
     * 获取月度财务数据
     *
     * @param reqVO 请求参数
     * @return 月度财务数据
     */
    AgencyFinanceMonthlyDataRespVO getMonthlyData(AgencyFinanceMonthlyDataReqVO reqVO);

    /**
     * 获取机构财务统计
     *
     * @param reqVO 请求参数
     * @return 财务统计信息
     */
    AgencyFinanceStatisticsRespVO getFinanceStatistics(AgencyFinanceStatisticsReqVO reqVO);

}
