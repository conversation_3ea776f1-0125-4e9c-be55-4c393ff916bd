package cn.bztmaster.cnt.module.publicbiz.controller.app.employer;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.*;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.GetUserCacheInfoRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.employer.MpUserLastLoginIdentityService;
import cn.bztmaster.cnt.module.publicbiz.service.employer.WxMiniProgramService;
import javax.annotation.security.PermitAll;
import cn.bztmaster.cnt.framework.tenant.core.context.TenantContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 小程序用户最后登录身份记录 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "用户 APP - 小程序用户最后登录身份记录")
@RestController
@RequestMapping("/publicbiz/employer/mpuser")
@Validated
@Slf4j
public class MpUserLastLoginIdentityController {

    @Resource
    private MpUserLastLoginIdentityService mpUserLastLoginIdentityService;

    @Resource
    private WxMiniProgramService wxMiniProgramService;

    @PostMapping("/create")
    @Operation(summary = "创建小程序用户最后登录身份记录")
    public CommonResult<Long> createMpUserLastLoginIdentity(
            @Valid @RequestBody MpUserLastLoginIdentitySaveReqVO createReqVO) {
        return success(mpUserLastLoginIdentityService.createMpUserLastLoginIdentity(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新小程序用户最后登录身份记录")
    public CommonResult<Boolean> updateMpUserLastLoginIdentity(
            @Valid @RequestBody MpUserLastLoginIdentitySaveReqVO updateReqVO) {
        mpUserLastLoginIdentityService.updateMpUserLastLoginIdentity(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除小程序用户最后登录身份记录")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteMpUserLastLoginIdentity(@RequestParam("id") Long id) {
        mpUserLastLoginIdentityService.deleteMpUserLastLoginIdentity(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得小程序用户最后登录身份记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<MpUserLastLoginIdentityRespVO> getMpUserLastLoginIdentity(@RequestParam("id") Long id) {
        MpUserLastLoginIdentityRespVO mpUserLastLoginIdentity = mpUserLastLoginIdentityService
                .getMpUserLastLoginIdentity(id);
        return success(mpUserLastLoginIdentity);
    }

    @GetMapping("/get-by-user-id-and-openid")
    @Operation(summary = "根据用户ID和openid获得小程序用户最后登录身份记录")
    @Parameter(name = "userId", description = "用户ID", required = true, example = "1024")
    @Parameter(name = "openid", description = "微信openid", required = true, example = "oH_Tu5EtrrF6n7u8v9w0x1y2z3")
    public CommonResult<MpUserLastLoginIdentityRespVO> getMpUserLastLoginIdentityByUserIdAndOpenid(
            @RequestParam("userId") Long userId, @RequestParam("openid") String openid) {
        MpUserLastLoginIdentityRespVO mpUserLastLoginIdentity = mpUserLastLoginIdentityService
                .getMpUserLastLoginIdentityByUserIdAndOpenid(userId, openid);
        return success(mpUserLastLoginIdentity);
    }

    @GetMapping("/get-by-openid")
    @Operation(summary = "根据openid获得小程序用户最后登录身份记录")
    @Parameter(name = "openid", description = "微信openid", required = true, example = "oH_Tu5EtrrF6n7u8v9w0x1y2z3")
    public CommonResult<MpUserLastLoginIdentityRespVO> getMpUserLastLoginIdentityByOpenid(
            @RequestParam("openid") String openid) {
        MpUserLastLoginIdentityRespVO mpUserLastLoginIdentity = mpUserLastLoginIdentityService
                .getMpUserLastLoginIdentityByOpenid(openid);
        return success(mpUserLastLoginIdentity);
    }

    @GetMapping("/get-user-cache-info")
    @Operation(summary = "根据openid获取用户缓存信息")
    @Parameter(name = "openid", description = "微信openid", required = true, example = "oH_Tu5EtrrF6n7u8v9w0x1y2z3")
    public CommonResult<GetUserCacheInfoRespVO> getUserCacheInfoByOpenid(
            @RequestParam("openid") String openid) {
        GetUserCacheInfoRespVO userCacheInfo = mpUserLastLoginIdentityService
                .getUserCacheInfoByOpenid(openid);
        return success(userCacheInfo);
    }

    @GetMapping("/get-by-user-id")
    @Operation(summary = "根据用户ID获得小程序用户最后登录身份记录")
    @Parameter(name = "userId", description = "用户ID", required = true, example = "1024")
    public CommonResult<MpUserLastLoginIdentityRespVO> getMpUserLastLoginIdentityByUserId(
            @RequestParam("userId") Long userId) {
        MpUserLastLoginIdentityRespVO mpUserLastLoginIdentity = mpUserLastLoginIdentityService
                .getMpUserLastLoginIdentityByUserId(userId);
        return success(mpUserLastLoginIdentity);
    }

    @PostMapping("/save-or-update")
    @Operation(summary = "保存或更新小程序用户最后登录身份记录")
    public CommonResult<Long> saveOrUpdateMpUserLastLoginIdentity(
            @Valid @RequestBody MpUserLastLoginIdentitySaveReqVO saveReqVO) {
        return success(mpUserLastLoginIdentityService.saveOrUpdateMpUserLastLoginIdentity(saveReqVO));
    }

    @PostMapping("/switch-identity")
    @Operation(summary = "切换用户身份")
    @PermitAll
    public CommonResult<SwitchIdentityRespVO> switchIdentity(@Valid @RequestBody SwitchIdentityReqVO reqVO) {
        // 设置租户上下文，避免租户验证失败
        TenantContextHolder.setIgnore(true);
        TenantContextHolder.setTenantId(1L);

        try {
            SwitchIdentityRespVO result = mpUserLastLoginIdentityService.switchIdentityAndGetUserInfo(
                    reqVO.getOpenid(), reqVO.getIdentityType());
            if (result == null) {
                return CommonResult.error(1003, "用户不存在或获取用户信息失败");
            }
            return success(result);
        } catch (Exception e) {
            log.error("切换用户身份失败", e);
            return CommonResult.error(5000, "服务器内部错误");
        }
    }

    @PostMapping("/getUserRole")
    @Operation(summary = "获取用户角色")
    @PermitAll
    public CommonResult<GetUserRoleRespVO> getUserRole(@Valid @RequestBody GetUserRoleReqVO reqVO) {
        // 设置租户上下文，避免租户验证失败
        TenantContextHolder.setIgnore(true);
        TenantContextHolder.setTenantId(1L);

        try {
            // 1. 调用微信接口获取openid
            WxCode2SessionRespVO wxResult = wxMiniProgramService.code2Session(reqVO.getCode());
            if (!wxResult.isSuccess()) {
                return CommonResult.error(1002, "微信登录失败: " + wxResult.getErrmsg());
            }

            // 2. 根据openid查询用户最后登录身份记录
            MpUserLastLoginIdentityRespVO lastLoginIdentity = mpUserLastLoginIdentityService
                    .getMpUserLastLoginIdentityByOpenid(wxResult.getOpenid());

            if (lastLoginIdentity == null) {
                return CommonResult.error(1003, "用户不存在，请先注册");
            }

            // 3. 检查用户状态
            if (lastLoginIdentity.getLoginStatus() != null && lastLoginIdentity.getLoginStatus() == 0) {
                return CommonResult.error(1004, "用户被禁用，请联系客服");
            }

            // 4. 获取用户缓存信息
            GetUserCacheInfoRespVO userCacheInfo = mpUserLastLoginIdentityService
                    .getUserCacheInfoByOpenid(wxResult.getOpenid());

            // 5. 构建用户信息
            UserInfoRespVO userInfo = new UserInfoRespVO();
            userInfo.setId(String.valueOf(lastLoginIdentity.getIdentityId()));
            userInfo.setOpenid(lastLoginIdentity.getOpenid());
            userInfo.setNickname(lastLoginIdentity.getIdentityName());
            // userInfo.setAvatar(""); // 可以从用户表获取头像
            // userInfo.setMobile(""); // 可以从用户表获取手机号
            userInfo.setRole(lastLoginIdentity.getIdentityType());
            // userInfo.setStatus("active");
            // userInfo.setCreateTime(lastLoginIdentity.getCreateTime());
            // userInfo.setUpdateTime(lastLoginIdentity.getUpdateTime());

            // 6. 生成访问令牌（这里简化处理，实际应该使用JWT）
            String token = "token_" + System.currentTimeMillis();
            String refreshToken = "refresh_token_" + System.currentTimeMillis();

            // 7. 构建响应数据
            GetUserRoleRespVO response = new GetUserRoleRespVO();
            response.setRole(lastLoginIdentity.getIdentityType());
            response.setUserInfo(userInfo);
            response.setToken(token);
            response.setRefreshToken(refreshToken);
            response.setUserCacheInfo(userCacheInfo);

            return success(response);

        } catch (Exception e) {
            log.error("获取用户角色失败", e);
            return CommonResult.error(5000, "服务器内部错误");
        }
    }

}
