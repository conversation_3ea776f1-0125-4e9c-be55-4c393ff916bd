package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取用户缓存信息响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "获取用户缓存信息响应 VO")
@Data
public class GetUserCacheInfoRespVO {

    @Schema(description = "身份类型：AGENCY-机构，EMPLOYER-雇主，AUNT-阿姨", requiredMode = Schema.RequiredMode.REQUIRED, example = "EMPLOYER")
    private String identityType;

    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    @Schema(description = "刷新令牌", example = "refresh_token_123456")
    private String refreshToken;

    @Schema(description = "机构用户信息（当identityType为AGENCY时返回）")
    private AgencyUserInfo agencyUserInfo;

    @Schema(description = "雇主用户信息（当identityType为EMPLOYER时返回）")
    private EmployerUserInfo employerUserInfo;

    @Schema(description = "阿姨用户信息（当identityType为AUNT时返回）")
    private AuntUserInfo auntUserInfo;

    @Schema(description = "机构用户信息")
    @Data
    public static class AgencyUserInfo {
        @Schema(description = "用户ID", example = "1024")
        private Long id;

        @Schema(description = "用户名", example = "admin")
        private String username;

        @Schema(description = "昵称", example = "张先生")
        private String nickname;

        @Schema(description = "手机号", example = "13812345678")
        private String mobile;

        @Schema(description = "头像", example = "https://example.com/avatar.jpg")
        private String avatar;

        @Schema(description = "部门ID", example = "100")
        private Long deptId;

        @Schema(description = "部门名称", example = "技术部")
        private String deptName;

        @Schema(description = "机构ID", example = "1")
        private Long agencyId;

        @Schema(description = "机构名称", example = "传能家政")
        private String agencyName;

        @Schema(description = "状态", example = "0")
        private Integer status;
    }

    @Schema(description = "雇主用户信息")
    @Data
    public static class EmployerUserInfo {
        @Schema(description = "用户ID", example = "1024")
        private Long id;

        @Schema(description = "OneID", example = "uuid-string")
        private String oneid;

        @Schema(description = "微信openid", example = "oH_Tu5EtrrF6n7u8v9w0x1y2z3")
        private String openid;

        @Schema(description = "昵称", example = "张先生")
        private String nickname;

        @Schema(description = "手机号", example = "13812345678")
        private String mobile;

        @Schema(description = "头像地址", example = "https://example.com/avatar.jpg")
        private String headImageUrl;

        @Schema(description = "关注状态", example = "1")
        private Integer subscribeStatus;

        @Schema(description = "关注时间")
        private String subscribeTime;

        @Schema(description = "语言", example = "zh_CN")
        private String language;

        @Schema(description = "国家", example = "中国")
        private String country;

        @Schema(description = "省份", example = "四川")
        private String province;

        @Schema(description = "城市", example = "成都")
        private String city;
    }

    @Schema(description = "阿姨用户信息")
    @Data
    public static class AuntUserInfo {
        @Schema(description = "OneID", example = "uuid-string")
        private String oneid;
    }
}
