package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 机构财务统计 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构端 - 机构财务统计 Response VO")
@Data
public class AgencyFinanceStatisticsRespVO {

    @Schema(description = "年份", example = "2025")
    private Integer year;

    @Schema(description = "年度总收入", example = "150000.00")
    private String totalIncome;

    @Schema(description = "年度总支出", example = "85000.00")
    private String totalExpense;

    @Schema(description = "年度净收入", example = "65000.00")
    private String netIncome;

    @Schema(description = "月度数据")
    private List<MonthlyData> monthlyData;

    @Schema(description = "月度数据")
    @Data
    public static class MonthlyData {

        @Schema(description = "月份", example = "1")
        private Integer month;

        @Schema(description = "收入", example = "12000.00")
        private String income;

        @Schema(description = "支出", example = "8000.00")
        private String expense;

    }

}
