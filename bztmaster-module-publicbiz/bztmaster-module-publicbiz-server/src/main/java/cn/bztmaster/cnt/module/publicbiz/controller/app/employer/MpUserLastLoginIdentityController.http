### 创建小程序用户最后登录身份记录
POST {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/create
Content-Type: application/json

{
  "userId": 1024,
  "openid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
  "unionid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
  "identityType": "EMPLOYER",
  "identityId": 1024,
  "identityName": "张先生",
  "lastLoginTime": "2024-01-01T12:00:00",
  "lastLoginIp": "***********",
  "deviceInfo": "iPhone 14 Pro",
  "loginStatus": 1,
  "sessionKey": "session_key_example",
  "accessToken": "access_token_example",
  "refreshToken": "refresh_token_example",
  "tokenExpireTime": "2024-01-02T12:00:00"
}

### 更新小程序用户最后登录身份记录
PUT {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/update
Content-Type: application/json

{
  "id": 1,
  "userId": 1024,
  "openid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
  "unionid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
  "identityType": "EMPLOYER",
  "identityId": 1024,
  "identityName": "张先生",
  "lastLoginTime": "2024-01-01T12:00:00",
  "lastLoginIp": "***********",
  "deviceInfo": "iPhone 14 Pro",
  "loginStatus": 1,
  "sessionKey": "session_key_example",
  "accessToken": "access_token_example",
  "refreshToken": "refresh_token_example",
  "tokenExpireTime": "2024-01-02T12:00:00"
}

### 删除小程序用户最后登录身份记录
DELETE {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/delete?id=1

### 获得小程序用户最后登录身份记录
GET {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/get?id=1

### 根据用户ID和openid获得小程序用户最后登录身份记录
GET {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/get-by-user-id-and-openid?userId=1024&openid=oH_Tu5EtrrF6n7u8v9w0x1y2z3

### 根据openid获得小程序用户最后登录身份记录
GET {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/get-by-openid?openid=oH_Tu5EtrrF6n7u8v9w0x1y2z3

### 根据openid获取用户缓存信息
GET {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/get-user-cache-info?openid=oH_Tu5EtrrF6n7u8v9w0x1y2z3

### 测试获取用户缓存信息并验证token返回
# 这个测试用例用于验证getUserCacheInfoByOpenid接口是否正确返回token和refreshToken
GET {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/get-user-cache-info?openid=test_openid_123456

# 预期响应应该包含：
# {
#   "code": 0,
#   "data": {
#     "identityType": "EMPLOYER",
#     "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
#     "refreshToken": "refresh_token_123456",
#     "employerUserInfo": {
#       "id": 1024,
#       "oneid": "uuid-string",
#       "openid": "test_openid_123456",
#       "nickname": "张先生",
#       "mobile": "13812345678",
#       "headImageUrl": "https://example.com/avatar.jpg",
#       "subscribeStatus": 1,
#       "subscribeTime": "2024-01-01T12:00:00",
#       "language": "zh_CN",
#       "country": "中国",
#       "province": "四川",
#       "city": "成都"
#     }
#   },
#   "msg": "操作成功"
# }

### 根据用户ID获得小程序用户最后登录身份记录
GET {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/get-by-user-id?userId=1024

### 保存或更新小程序用户最后登录身份记录
POST {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/save-or-update
Content-Type: application/json

{
  "userId": 1024,
  "openid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
  "unionid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
  "identityType": "EMPLOYER",
  "identityId": 1024,
  "identityName": "张先生",
  "lastLoginTime": "2024-01-01T12:00:00",
  "lastLoginIp": "***********",
  "deviceInfo": "iPhone 14 Pro",
  "loginStatus": 1,
  "sessionKey": "session_key_example",
  "accessToken": "access_token_example",
  "refreshToken": "refresh_token_example",
  "tokenExpireTime": "2024-01-02T12:00:00"
}

### 获取用户角色
POST {{baseUrl}}/publicbiz/employer/mpuser/getUserRole
Content-Type: application/json

{
  "code": "wx_login_code_123456"
}

### 切换用户身份
POST {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/switch-identity
Content-Type: application/json

{
  "openid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
  "identityType": "EMPLOYER"
}

### 测试切换用户身份并验证返回的用户信息和token
# 这个测试用例用于验证switch-identity接口是否正确返回用户信息和token
POST {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/switch-identity
Content-Type: application/json

{
  "openid": "test_openid_123456",
  "identityType": "EMPLOYER"
}

# 预期响应应该包含：
# {
#   "code": 0,
#   "data": {
#     "id": 1024,
#     "identityType": "EMPLOYER",
#     "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
#     "refreshToken": "refresh_token_123456",
#     "employerUserInfo": {
#       "id": 1024,
#       "oneid": "uuid-string",
#       "openid": "test_openid_123456",
#       "nickname": "张先生",
#       "mobile": "13812345678",
#       "headImageUrl": "https://example.com/avatar.jpg",
#       "subscribeStatus": 1,
#       "subscribeTime": "2024-01-01T12:00:00",
#       "language": "zh_CN",
#       "country": "中国",
#       "province": "四川",
#       "city": "成都"
#     }
#   },
#   "msg": "操作成功"
# } 