package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取用户角色响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "获取用户角色响应 VO")
@Data
public class GetUserRoleRespVO {

    @Schema(description = "用户角色", example = "employer")
    private String role;

    @Schema(description = "用户基本信息")
    private UserInfoRespVO userInfo;

    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    @Schema(description = "刷新令牌", example = "refresh_token_123456")
    private String refreshToken;

    @Schema(description = "用户缓存信息")
    private GetUserCacheInfoRespVO userCacheInfo;

}