package cn.bztmaster.cnt.module.publicbiz.service.employer;

import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.MpUserLastLoginIdentityRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.MpUserLastLoginIdentitySaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.GetUserCacheInfoRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserLastLoginIdentityDO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.SwitchIdentityRespVO;

import javax.validation.Valid;

/**
 * 小程序用户最后登录身份记录 Service 接口
 *
 * <AUTHOR>
 */
public interface MpUserLastLoginIdentityService {

    /**
     * 创建小程序用户最后登录身份记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMpUserLastLoginIdentity(@Valid MpUserLastLoginIdentitySaveReqVO createReqVO);

    /**
     * 更新小程序用户最后登录身份记录
     *
     * @param updateReqVO 更新信息
     */
    void updateMpUserLastLoginIdentity(@Valid MpUserLastLoginIdentitySaveReqVO updateReqVO);

    /**
     * 删除小程序用户最后登录身份记录
     *
     * @param id 编号
     */
    void deleteMpUserLastLoginIdentity(Long id);

    /**
     * 获得小程序用户最后登录身份记录
     *
     * @param id 编号
     * @return 小程序用户最后登录身份记录
     */
    MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentity(Long id);

    /**
     * 根据用户ID和openid获得小程序用户最后登录身份记录
     *
     * @param userId 用户ID
     * @param openid 微信openid
     * @return 小程序用户最后登录身份记录
     */
    MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentityByUserIdAndOpenid(Long userId, String openid);

    /**
     * 根据openid获得小程序用户最后登录身份记录
     *
     * @param openid 微信openid
     * @return 小程序用户最后登录身份记录
     */
    MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentityByOpenid(String openid);

    /**
     * 根据用户ID获得小程序用户最后登录身份记录
     *
     * @param userId 用户ID
     * @return 小程序用户最后登录身份记录
     */
    MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentityByUserId(Long userId);

    /**
     * 保存或更新小程序用户最后登录身份记录
     *
     * @param saveReqVO 保存信息
     * @return 编号
     */
    Long saveOrUpdateMpUserLastLoginIdentity(@Valid MpUserLastLoginIdentitySaveReqVO saveReqVO);

    /**
     * 根据openid切换用户身份
     *
     * @param openid       微信openid
     * @param identityType 身份类型
     * @return 编号
     */
    Long switchIdentityByOpenid(String openid, String identityType);

    /**
     * 根据openid获取用户缓存信息
     *
     * @param openid 微信openid
     * @return 用户缓存信息
     */
    GetUserCacheInfoRespVO getUserCacheInfoByOpenid(String openid);

    /**
     * 切换用户身份并返回用户信息和token
     *
     * @param openid       微信openid
     * @param identityType 身份类型
     * @return 切换身份响应信息
     */
    SwitchIdentityRespVO switchIdentityAndGetUserInfo(String openid, String identityType);

}