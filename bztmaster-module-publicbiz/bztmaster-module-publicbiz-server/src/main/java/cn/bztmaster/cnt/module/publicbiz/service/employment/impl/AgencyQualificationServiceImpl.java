package cn.bztmaster.cnt.module.publicbiz.service.employment.impl;

import cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationCreateVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationUpdateVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationBatchUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.AgencyConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyQualificationDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyQualificationMapper;
import cn.bztmaster.cnt.module.publicbiz.enums.AgencyQualificationFileTypeEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.AgencyQualificationStatusEnum;
import cn.bztmaster.cnt.module.publicbiz.service.employment.AgencyQualificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.AGENCY_QUALIFICATION_NOT_EXISTS;

/**
 * 机构资质文件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AgencyQualificationServiceImpl implements AgencyQualificationService {

    @Resource
    private AgencyQualificationMapper agencyQualificationMapper;

    @Resource
    private AgencyMapper agencyMapper;

    @Override
    public List<AgencyQualificationDO> getQualificationsByAgencyId(Long agencyId) {
        return agencyQualificationMapper.selectListByAgencyId(agencyId);
    }

    @Override
    public AgencyQualificationDO createQualification(AgencyQualificationCreateVO createVO) {
        AgencyQualificationDO qualification = AgencyConvert.INSTANCE.convert(createVO);
        agencyQualificationMapper.insert(qualification);
        return qualification;
    }

    @Override
    public void updateQualification(AgencyQualificationUpdateVO updateVO) {
        // 校验存在
        validateQualificationExists(updateVO.getId());
        // 更新
        AgencyQualificationDO updateObj = AgencyConvert.INSTANCE.convert(updateVO);
        agencyQualificationMapper.updateById(updateObj);
    }

    @Override
    public void deleteQualification(Long id) {
        // 校验存在
        validateQualificationExists(id);
        // 删除
        agencyQualificationMapper.deleteById(id);
    }

    @Override
    public void deleteQualificationsByAgencyId(Long agencyId) {
        agencyQualificationMapper.deleteByAgencyId(agencyId);
    }

    @Override
    public AgencyQualificationDO getQualificationById(Long id) {
        return agencyQualificationMapper.selectById(id);
    }

    private void validateQualificationExists(Long id) {
        if (agencyQualificationMapper.selectById(id) == null) {
            throw ServiceExceptionUtil.exception(AGENCY_QUALIFICATION_NOT_EXISTS);
        }
    }

    @Override
    public cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyQualificationRespVO getAgencyQualificationsForApp(Long agencyId) {
        // 获取机构信息
        AgencyDO agency = agencyMapper.selectById(agencyId);
        if (agency == null) {
            throw ServiceExceptionUtil.exception(AGENCY_QUALIFICATION_NOT_EXISTS);
        }

        // 获取资质文件列表
        List<AgencyQualificationDO> qualifications = agencyQualificationMapper.selectListByAgencyId(agencyId);

        // 转换为小程序端响应VO
        List<cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyQualificationRespVO.QualificationFile> qualificationFiles =
            qualifications.stream().map(this::convertToAppQualificationFile).collect(Collectors.toList());

        // 构建响应对象
        cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyQualificationRespVO respVO =
            new cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyQualificationRespVO();
        respVO.setAgencyId(agencyId);
        respVO.setAgencyName(agency.getAgencyName());
        respVO.setQualifications(qualificationFiles);

        return respVO;
    }

    /**
     * 转换为小程序端资质文件对象
     */
    private cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyQualificationRespVO.QualificationFile convertToAppQualificationFile(AgencyQualificationDO qualification) {
        cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyQualificationRespVO.QualificationFile file =
            new cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyQualificationRespVO.QualificationFile();

        file.setId(qualification.getId());
        file.setFileType(qualification.getFileType());
        file.setFileName(qualification.getFileName());
        file.setFileUrl(qualification.getFileUrl());
        file.setFileSize(qualification.getFileSize());
        file.setFileExtension(qualification.getFileExtension());
        file.setSortOrder(qualification.getSortOrder());
        file.setStatus(qualification.getStatus());

        // 设置文件类型名称
        AgencyQualificationFileTypeEnum fileTypeEnum = AgencyQualificationFileTypeEnum.getByValue(qualification.getFileType());
        file.setFileTypeName(fileTypeEnum != null ? fileTypeEnum.getName() : "未知类型");

        // 设置状态名称
        AgencyQualificationStatusEnum statusEnum = AgencyQualificationStatusEnum.getByValue(qualification.getStatus());
        file.setStatusName(statusEnum != null ? statusEnum.getName() : "未知状态");

        return file;
    }

    @Override
    public List<AgencyQualificationListRespVO> getQualificationList(AgencyQualificationListReqVO reqVO) {
        // 根据机构ID和文件类型查询资质文件列表
        List<AgencyQualificationDO> qualifications = agencyQualificationMapper
                .selectListByAgencyIdAndFileTypes(reqVO.getAgencyId(), reqVO.getFileType());

        // 转换为响应VO
        return AgencyConvert.INSTANCE.convertQualificationListResp(qualifications);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateQualifications(AgencyQualificationBatchUpdateReqVO reqVO) {
        // 定义必需的文件类型
        List<String> requiredFileTypes = Arrays.asList(
                "business_license",      // 营业执照
                "account_opening_permit", // 开户许可证
                "door_photo",            // 门头照
                "id_card_front",         // 法人身份证人像面
                "id_card_back"           // 法人身份证国徽面
        );

        // 按机构ID分组处理
        Map<Long, List<AgencyQualificationBatchUpdateReqVO.FileInfo>> agencyFileMap = reqVO.getFiles()
                .stream()
                .collect(Collectors.groupingBy(AgencyQualificationBatchUpdateReqVO.FileInfo::getAgencyId));

        for (Map.Entry<Long, List<AgencyQualificationBatchUpdateReqVO.FileInfo>> entry : agencyFileMap.entrySet()) {
            Long agencyId = entry.getKey();
            List<AgencyQualificationBatchUpdateReqVO.FileInfo> files = entry.getValue();

            // 1. 处理上传的文件：删除旧记录，插入新记录
            for (AgencyQualificationBatchUpdateReqVO.FileInfo fileInfo : files) {
                // 删除相同agencyId和fileType的旧记录
                agencyQualificationMapper.deleteByAgencyIdAndFileType(agencyId, fileInfo.getFileType());

                // 插入新记录
                AgencyQualificationDO qualification = new AgencyQualificationDO();
                qualification.setAgencyId(fileInfo.getAgencyId());
                qualification.setFileType(fileInfo.getFileType());
                qualification.setFileCategory(fileInfo.getFileCategory());
                qualification.setFileDescription(fileInfo.getFileDescription());
                qualification.setFileName(fileInfo.getFileName());
                qualification.setFileUrl(fileInfo.getFileUrl());
                qualification.setFileSize(fileInfo.getFileSize());
                qualification.setFileExtension(fileInfo.getFileExtension());
                qualification.setSortOrder(fileInfo.getSortOrder() != null ? fileInfo.getSortOrder() : 0);
                qualification.setStatus(1); // 设置为有效状态

                agencyQualificationMapper.insert(qualification);
            }

            // 2. 检查必需文件类型，删除缺失的类型记录
            Set<String> uploadedFileTypes = files.stream()
                    .map(AgencyQualificationBatchUpdateReqVO.FileInfo::getFileType)
                    .collect(Collectors.toSet());

            List<String> missingRequiredTypes = requiredFileTypes.stream()
                    .filter(type -> !uploadedFileTypes.contains(type))
                    .collect(Collectors.toList());

            if (!missingRequiredTypes.isEmpty()) {
                // 删除缺失的必需文件类型记录
                agencyQualificationMapper.deleteByAgencyIdAndFileTypes(agencyId, missingRequiredTypes);
            }
        }
    }


}
