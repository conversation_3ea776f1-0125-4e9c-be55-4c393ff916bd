package cn.bztmaster.cnt.module.publicbiz.convert.employment;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.employment.dto.AgencyPageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.employment.dto.AgencyRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.employment.dto.AgencyUpdateReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyCreateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationCreateVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationUpdateVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyQualificationRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyQualificationDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 机构 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AgencyConvert {

    AgencyConvert INSTANCE = Mappers.getMapper(AgencyConvert.class);

    AgencyRespVO convert(AgencyDO bean);

    AgencyRespDTO convert(AgencyRespVO bean);

    PageResult<AgencyRespVO> convertPage(PageResult<AgencyDO> page);

    List<AgencyRespVO> convertList(List<AgencyDO> list);

    PageResult<AgencyRespDTO> convertPageToDTO(PageResult<AgencyRespVO> page);

    AgencyPageReqVO convert(AgencyPageReqDTO bean);

    AgencyUpdateReqVO convert(AgencyUpdateReqDTO bean);

    List<AgencyQualificationVO> convertQualificationList(List<AgencyQualificationDO> list);

    List<AgencyQualificationRespVO.QualificationFile> convertAppQualificationList(List<AgencyQualificationDO> list);

    AgencyDO convert(AgencyCreateReqVO bean);

    AgencyQualificationDO convert(AgencyQualificationCreateVO bean);

    AgencyQualificationDO convert(AgencyQualificationUpdateVO bean);

    List<AgencyQualificationListRespVO> convertQualificationListResp(List<AgencyQualificationDO> list);

    AgencyQualificationListRespVO convertQualificationResp(AgencyQualificationDO bean);
}