package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * 机构财务统计 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构端 - 机构财务统计 Request VO")
@Data
public class AgencyFinanceStatisticsReqVO {

    @Schema(description = "机构ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotBlank(message = "机构ID不能为空")
    private String agencyId;

    @Schema(description = "年份", example = "2025")
    @Min(value = 2020, message = "年份不能小于2020")
    @Max(value = 2030, message = "年份不能大于2030")
    private Integer year;

}
