package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 机构财务月度数据 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构端 - 机构财务月度数据 Request VO")
@Data
public class AgencyFinanceMonthlyDataReqVO {

    @Schema(description = "机构ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotBlank(message = "机构ID不能为空")
    private String agencyId;

    @Schema(description = "年份", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025")
    @NotNull(message = "年份不能为空")
    @Min(value = 2020, message = "年份不能小于2020")
    @Max(value = 2030, message = "年份不能大于2030")
    private Integer year;

    @Schema(description = "月份", requiredMode = Schema.RequiredMode.REQUIRED, example = "7")
    @NotNull(message = "月份不能为空")
    @Min(value = 1, message = "月份不能小于1")
    @Max(value = 12, message = "月份不能大于12")
    private Integer month;

    @Schema(description = "交易类型筛选", example = "all")
    private String type = "all"; // all-全部，income-收入，expense-支出

    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNo = 1;

    @Schema(description = "每页数量", example = "20")
    @Min(value = 1, message = "每页数量不能小于1")
    @Max(value = 100, message = "每页数量不能大于100")
    private Integer pageSize = 20;

}
