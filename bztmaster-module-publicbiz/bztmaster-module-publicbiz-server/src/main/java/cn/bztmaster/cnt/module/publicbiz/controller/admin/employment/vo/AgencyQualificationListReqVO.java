package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 机构资质文件列表查询请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构资质文件列表查询请求 VO")
@Data
public class AgencyQualificationListReqVO {

    @Schema(description = "机构ID", example = "1001", required = true)
    @NotNull(message = "机构ID不能为空")
    private Long agencyId;

    @Schema(description = "文件类型数组", example = "[\"business_license\", \"qualification_cert\"]")
    private List<String> fileType;
}
