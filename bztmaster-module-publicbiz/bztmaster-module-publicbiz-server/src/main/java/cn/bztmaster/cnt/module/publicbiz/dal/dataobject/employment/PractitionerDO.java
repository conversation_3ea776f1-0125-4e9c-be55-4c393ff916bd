package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@TableName("publicbiz_practitioner")
@Schema(description = "阿姨基本信息表 DO")
public class PractitionerDO extends BaseDO {
    @TableId
    private Long id;
    private String auntOneid;
    private String name;
    private String phone;
    private String idCard;
    private String hometown;
    private Integer age;
    private String gender;
    private String avatar;
    private String serviceType;
    private Integer experienceYears;
    private String platformStatus;
    private BigDecimal rating;
    private Long agencyId;
    private String agencyName;
    private String status;
    private String applicationId;
    private LocalDateTime submitTime;
    private String currentStatus;
    private String currentOrderId;
    private Integer totalOrders;
    private BigDecimal totalIncome;
    private BigDecimal customerSatisfaction;
    private Long tenantId;
    private String profile;
    private LocalDateTime terminatedTime;
}
