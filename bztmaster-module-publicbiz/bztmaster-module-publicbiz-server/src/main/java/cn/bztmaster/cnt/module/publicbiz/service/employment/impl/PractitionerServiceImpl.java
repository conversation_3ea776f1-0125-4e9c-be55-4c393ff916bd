package cn.bztmaster.cnt.module.publicbiz.service.employment.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.framework.excel.core.util.ExcelUtils;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.PractitionerConvert;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.PractitionerQualificationConvert;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.PractitionerRatingRecordConvert;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.PractitionerServiceRecordConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerQualificationDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerRatingRecordDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerServiceRecordDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerQualificationMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerRatingRecordMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerServiceRecordMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic.DomesticTaskMapper;
import cn.bztmaster.cnt.module.publicbiz.service.employment.PractitionerService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants.*;

@Service
@Validated
@Slf4j
public class PractitionerServiceImpl implements PractitionerService {

    @Resource
    private PractitionerMapper practitionerMapper;

    @Resource
    private PractitionerQualificationMapper practitionerQualificationMapper;

    @Resource
    private PractitionerServiceRecordMapper practitionerServiceRecordMapper;

    @Resource
    private PractitionerRatingRecordMapper practitionerRatingRecordMapper;

    @Resource
    private DomesticTaskMapper domesticTaskMapper;

    @Override
    @Transactional
    @LogRecord(type = PRACTITIONER_TYPE, subType = PRACTITIONER_CREATE_SUB_TYPE, bizNo = "{{#result}}", success = PRACTITIONER_CREATE_SUCCESS)
    public Long createPractitioner(PractitionerSaveReqVO createReqVO) {
        // 1. 校验手机号和身份证号唯一性
        validatePhoneUnique(createReqVO.getPhone(), null);
        validateIdCardUnique(createReqVO.getIdCard(), null);

        // 2. 插入主表
        PractitionerDO practitioner = PractitionerConvert.INSTANCE.convert(createReqVO);
        // 生成GUID作为auntOneid
        String auntOneId = java.util.UUID.randomUUID().toString().replace("-", "");
        practitioner.setAuntOneid(auntOneId);
        practitioner.setCurrentStatus("待岗");
        practitioner.setTenantId(
                SecurityFrameworkUtils.getLoginUser() != null ? SecurityFrameworkUtils.getLoginUser().getTenantId()
                        : 1L);
        practitionerMapper.insert(practitioner);
        // 3. 插入资质文件
        if (createReqVO.getQualifications() != null) {
            for (PractitionerQualificationSaveReqVO qualificationVO : createReqVO.getQualifications()) {
                PractitionerQualificationDO qualification = PractitionerQualificationConvert.INSTANCE
                        .convert(qualificationVO);
                qualification.setPractitionerOneId(practitioner.getAuntOneid());
                qualification.setCreateTime(new Date());
                qualification.setDeleted(false);
                qualification.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
                qualification.setTenantId(SecurityFrameworkUtils.getLoginUser() != null
                        ? SecurityFrameworkUtils.getLoginUser().getTenantId()
                        : 1L);
                practitionerQualificationMapper.insert(qualification);
            }
        }

        // 记录操作日志上下文
        LogRecordContext.putVariable("practitioner", practitioner);
        return practitioner.getId();
    }

    @Override
    @Transactional
    @LogRecord(type = PRACTITIONER_TYPE, subType = PRACTITIONER_UPDATE_SUB_TYPE, bizNo = "{{#reqVO.id}}", success = PRACTITIONER_UPDATE_SUCCESS)
    public void updatePractitioner(PractitionerUpdateReqVO updateReqVO) {
        // 1. 校验存在
        PractitionerDO oldPractitioner = validatePractitionerExists(updateReqVO.getId());

        // 2. 校验手机号和身份证号唯一性
        validatePhoneUnique(updateReqVO.getPhone(), updateReqVO.getId());
        validateIdCardUnique(updateReqVO.getIdCard(), updateReqVO.getId());

        // 3. 更新主表
        PractitionerDO practitioner = PractitionerConvert.INSTANCE.convert(updateReqVO);
        practitionerMapper.updateById(practitioner);

        // 4. 智能更新资质文件（有ID的更新，没有ID的新增，删除不存在的）
        String qualificationChangeLog = updatePractitionerQualifications(practitioner.getId(),
                updateReqVO.getQualifications());

        // 手动比较字段变更，避免_DIFF函数的误报问题
        StringBuilder fieldChanges = new StringBuilder();

        // Compare basic fields
        if (!Objects.equals(oldPractitioner.getName(), updateReqVO.getName())) {
            fieldChanges.append("【阿姨姓名】从【").append(oldPractitioner.getName()).append("】修改为【")
                    .append(updateReqVO.getName()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getPhone(), updateReqVO.getPhone())) {
            fieldChanges.append("【手机号】从【").append(oldPractitioner.getPhone()).append("】修改为【")
                    .append(updateReqVO.getPhone()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getIdCard(), updateReqVO.getIdCard())) {
            fieldChanges.append("【身份证号】从【").append(oldPractitioner.getIdCard()).append("】修改为【")
                    .append(updateReqVO.getIdCard()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getHometown(), updateReqVO.getHometown())) {
            fieldChanges.append("【籍贯】从【").append(oldPractitioner.getHometown()).append("】修改为【")
                    .append(updateReqVO.getHometown()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getAge(), updateReqVO.getAge())) {
            fieldChanges.append("【年龄】从【").append(oldPractitioner.getAge()).append("】修改为【").append(updateReqVO.getAge())
                    .append("】；");
        }
        if (!Objects.equals(oldPractitioner.getGender(), updateReqVO.getGender())) {
            fieldChanges.append("【性别】从【").append(oldPractitioner.getGender()).append("】修改为【")
                    .append(updateReqVO.getGender()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getAvatar(), updateReqVO.getAvatar())) {
            fieldChanges.append("【头像】从【").append(oldPractitioner.getAvatar()).append("】修改为【")
                    .append(updateReqVO.getAvatar()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getServiceType(), updateReqVO.getServiceType())) {
            fieldChanges.append("【主要服务类型】从【").append(oldPractitioner.getServiceType()).append("】修改为【")
                    .append(updateReqVO.getServiceType()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getExperienceYears(), updateReqVO.getExperienceYears())) {
            fieldChanges.append("【从业年限】从【").append(oldPractitioner.getExperienceYears()).append("】修改为【")
                    .append(updateReqVO.getExperienceYears()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getPlatformStatus(), updateReqVO.getPlatformStatus())) {
            fieldChanges.append("【平台状态】从【").append(oldPractitioner.getPlatformStatus()).append("】修改为【")
                    .append(updateReqVO.getPlatformStatus()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getRating(), updateReqVO.getRating())) {
            fieldChanges.append("【评级】从【").append(oldPractitioner.getRating()).append("】修改为【")
                    .append(updateReqVO.getRating()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getAgencyId(), updateReqVO.getAgencyId())) {
            fieldChanges.append("【所属机构ID】从【").append(oldPractitioner.getAgencyId()).append("】修改为【")
                    .append(updateReqVO.getAgencyId()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getAgencyName(), updateReqVO.getAgencyName())) {
            fieldChanges.append("【所属机构名称】从【").append(oldPractitioner.getAgencyName()).append("】修改为【")
                    .append(updateReqVO.getAgencyName()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getStatus(), updateReqVO.getStatus())) {
            fieldChanges.append("【状态】从【").append(oldPractitioner.getStatus()).append("】修改为【")
                    .append(updateReqVO.getStatus()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getCurrentStatus(), updateReqVO.getCurrentStatus())) {
            fieldChanges.append("【当前状态】从【").append(oldPractitioner.getCurrentStatus()).append("】修改为【")
                    .append(updateReqVO.getCurrentStatus()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getCurrentOrderId(), updateReqVO.getCurrentOrderId())) {
            fieldChanges.append("【当前服务订单ID】从【").append(oldPractitioner.getCurrentOrderId()).append("】修改为【")
                    .append(updateReqVO.getCurrentOrderId()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getTotalOrders(), updateReqVO.getTotalOrders())) {
            fieldChanges.append("【累计服务单数】从【").append(oldPractitioner.getTotalOrders()).append("】修改为【")
                    .append(updateReqVO.getTotalOrders()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getTotalIncome(), updateReqVO.getTotalIncome())) {
            fieldChanges.append("【累计收入】从【").append(oldPractitioner.getTotalIncome()).append("】修改为【")
                    .append(updateReqVO.getTotalIncome()).append("】；");
        }
        if (!Objects.equals(oldPractitioner.getCustomerSatisfaction(), updateReqVO.getCustomerSatisfaction())) {
            fieldChanges.append("【客户满意度评分】从【").append(oldPractitioner.getCustomerSatisfaction()).append("】修改为【")
                    .append(updateReqVO.getCustomerSatisfaction()).append("】；");
        }

        // Record operation log context
        LogRecordContext.putVariable("practitioner", oldPractitioner);
        LogRecordContext.putVariable("updateReqVO", updateReqVO);
        LogRecordContext.putVariable("fieldChanges", fieldChanges.toString());

        // If there are qualification changes, add to log context
        if (qualificationChangeLog != null && !qualificationChangeLog.isEmpty()) {
            LogRecordContext.putVariable("qualificationChangeLog", qualificationChangeLog);
        }
    }

    @Override
    public PractitionerRespVO getPractitioner(Long id) {
        // 查询主表（过滤已软删除的数据）
        PractitionerDO practitioner = practitionerMapper.selectOne(
                new LambdaQueryWrapperX<PractitionerDO>()
                        .eq(PractitionerDO::getId, id)
                        .eq(PractitionerDO::getDeleted, false));
        if (practitioner == null) {
            return null;
        }

        PractitionerRespVO respVO = PractitionerConvert.INSTANCE.convert(practitioner);
        // 手动设置阿姨OneID，确保字段正确映射
        respVO.setAuntOneid(practitioner.getAuntOneid());

        // 查询资质文件（过滤已软删除的数据）
        List<PractitionerQualificationDO> qualificationList = practitionerQualificationMapper.selectList(
                new LambdaQueryWrapperX<PractitionerQualificationDO>()
                        .eq(PractitionerQualificationDO::getPractitionerOneId, practitioner.getAuntOneid())
                        .eq(PractitionerQualificationDO::getDeleted, false)
                        .orderByAsc(PractitionerQualificationDO::getSortOrder));
        respVO.setQualifications(PractitionerQualificationConvert.INSTANCE.convertList(qualificationList));

        // 查询最近服务记录（过滤已软删除的数据）
        List<PractitionerServiceRecordDO> serviceRecordList = practitionerServiceRecordMapper.selectList(
                new LambdaQueryWrapperX<PractitionerServiceRecordDO>()
                        .eq(PractitionerServiceRecordDO::getPractitionerOneid, practitioner.getAuntOneid())
                        .eq(PractitionerServiceRecordDO::getDeleted, false)
                        .orderByDesc(PractitionerServiceRecordDO::getCreateTime)
                        .last("LIMIT 10"));
        respVO.setRecentServiceRecords(PractitionerServiceRecordConvert.INSTANCE.convertList(serviceRecordList));

        // 查询评级历史记录（过滤已软删除的数据）
        List<PractitionerRatingRecordDO> ratingRecordList = practitionerRatingRecordMapper.selectList(
                new LambdaQueryWrapperX<PractitionerRatingRecordDO>()
                        .eq(PractitionerRatingRecordDO::getPractitionerOneid, practitioner.getAuntOneid())
                        .eq(PractitionerRatingRecordDO::getDeleted, false)
                        .orderByDesc(PractitionerRatingRecordDO::getCreateTime)
                        .last("LIMIT 10"));
        respVO.setRatingRecords(PractitionerRatingRecordConvert.INSTANCE.convertList(ratingRecordList));

        return respVO;
    }

    @Override
    public PageResult<PractitionerRespVO> getPractitionerPage(PractitionerPageReqVO pageReqVO) {
        // 1. 查询主表（过滤已软删除的数据）
        PageResult<PractitionerDO> pageResult = practitionerMapper.selectPage(pageReqVO,
                new LambdaQueryWrapperX<PractitionerDO>()
                        .likeIfPresent(PractitionerDO::getName, pageReqVO.getKeyword())
                        .likeIfPresent(PractitionerDO::getPhone, pageReqVO.getKeyword())
                        .eqIfPresent(PractitionerDO::getServiceType, pageReqVO.getServiceType())
                        .eqIfPresent(PractitionerDO::getPlatformStatus, pageReqVO.getPlatformStatus())
                        .eqIfPresent(PractitionerDO::getRating, pageReqVO.getRating())
                        .eqIfPresent(PractitionerDO::getAgencyId, pageReqVO.getAgencyId())
                        .eq(PractitionerDO::getDeleted, false) // 只查询未删除的数据
                        .orderByDesc(PractitionerDO::getId));

        // 2. 转换为VO
        return PractitionerConvert.INSTANCE.convertPage(pageResult);
    }

    @Override
    @Transactional
    @LogRecord(type = PRACTITIONER_TYPE, subType = PRACTITIONER_STATUS_UPDATE_SUB_TYPE, bizNo = "{{#reqVO.id}}", success = PRACTITIONER_STATUS_UPDATE_SUCCESS)
    public void updatePractitionerStatus(PractitionerStatusUpdateReqVO reqVO) {
        // 1. 校验存在
        PractitionerDO practitioner = validatePractitionerExists(reqVO.getId());

        // 2. 如果状态为"已解约"，检查是否有执行中的任务
        if ("已解约".equals(reqVO.getPlatformStatus()) || "terminated".equals(reqVO.getPlatformStatus())) {
            List<DomesticTaskDO> inProgressTasks = domesticTaskMapper.selectInProgressByAuntOneId(practitioner.getAuntOneid());
            if (inProgressTasks != null && !inProgressTasks.isEmpty()) {
                throw new RuntimeException("该阿姨当前有执行中的任务，无法解约。请先完成或取消所有执行中的任务后再进行解约操作。");
            }
        }

        // 3. 更新状态
        practitioner.setPlatformStatus(reqVO.getPlatformStatus());
        practitioner.setTerminatedTime(java.time.LocalDateTime.now());
        practitioner.setStatus("inactive");
        practitionerMapper.updateById(practitioner);

        // 记录操作日志上下文
        LogRecordContext.putVariable("practitioner", practitioner);
        LogRecordContext.putVariable("reqVO", reqVO);
    }

    @Override
    @Transactional
    @LogRecord(type = PRACTITIONER_TYPE, subType = PRACTITIONER_RATING_UPDATE_SUB_TYPE, bizNo = "{{#reqVO.id}}", success = PRACTITIONER_RATING_UPDATE_SUCCESS)
    public void updatePractitionerRating(PractitionerRatingUpdateReqVO reqVO) {
        // 1. 校验存在
        PractitionerDO practitioner = validatePractitionerExists(reqVO.getId());

        // 2. 记录评级变化
        BigDecimal oldRating = practitioner.getRating();
        BigDecimal newRating = reqVO.getNewRating();
        BigDecimal ratingChange = newRating.subtract(oldRating);

        // 3. 更新主表评级
        practitioner.setRating(newRating);
        practitionerMapper.updateById(practitioner);

        // 4. 插入评级记录
        PractitionerRatingRecordDO ratingRecord = new PractitionerRatingRecordDO();
        ratingRecord.setPractitionerOneid(reqVO.getPractitionerOneId());
        ratingRecord.setRatingType(reqVO.getRatingType() != null ? reqVO.getRatingType() : "platform");
        ratingRecord.setOldRating(oldRating);
        ratingRecord.setNewRating(newRating);
        ratingRecord.setRatingChange(ratingChange);
        ratingRecord.setRatingReason(reqVO.getRatingReason());
        ratingRecord.setEvaluatorId(SecurityFrameworkUtils.getLoginUserId());
        ratingRecord.setEvaluatorName(SecurityFrameworkUtils.getLoginUserNickname());
        ratingRecord.setEvaluatorType("admin");
        ratingRecord.setCreateTime(new Date());
        ratingRecord.setDeleted(false);
        ratingRecord.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
        ratingRecord.setTenantId(
                SecurityFrameworkUtils.getLoginUser() != null ? SecurityFrameworkUtils.getLoginUser().getTenantId()
                        : 1L);
        practitionerRatingRecordMapper.insert(ratingRecord);

        // 记录操作日志上下文
        LogRecordContext.putVariable("practitioner", practitioner);
        LogRecordContext.putVariable("reqVO", reqVO);
    }

    @Override
    public List<PractitionerDO> getPractitionerList(PractitionerPageReqVO pageReqVO) {
        // 查询数据（过滤已软删除的数据）
        List<PractitionerDO> list = practitionerMapper.selectList(new LambdaQueryWrapperX<PractitionerDO>()
                .likeIfPresent(PractitionerDO::getName, pageReqVO.getKeyword())
                .likeIfPresent(PractitionerDO::getPhone, pageReqVO.getKeyword())
                .eqIfPresent(PractitionerDO::getServiceType, pageReqVO.getServiceType())
                .eqIfPresent(PractitionerDO::getPlatformStatus, pageReqVO.getPlatformStatus())
                .eqIfPresent(PractitionerDO::getRating, pageReqVO.getRating())
                .eqIfPresent(PractitionerDO::getAgencyId, pageReqVO.getAgencyId())
                .eq(PractitionerDO::getDeleted, false) // 只查询未删除的数据
                .orderByDesc(PractitionerDO::getId));

        // 出参 platformStatus 映射：cooperating -> 合作中，terminated -> 已解约
        for (PractitionerDO item : list) {
            String status = item.getPlatformStatus();
            if ("cooperating".equals(status)) {
                item.setPlatformStatus("合作中");
            } else if ("terminated".equals(status)) {
                item.setPlatformStatus("已解约");
            }
        }
        return list;
    }

    private PractitionerDO validatePractitionerExists(Long id) {
        PractitionerDO practitioner = practitionerMapper.selectOne(
                new LambdaQueryWrapperX<PractitionerDO>()
                        .eq(PractitionerDO::getId, id)
                        .eq(PractitionerDO::getDeleted, false));
        if (practitioner == null) {
            throw new RuntimeException("阿姨不存在");
        }
        return practitioner;
    }

    private void validatePhoneUnique(String phone, Long excludeId) {
        LambdaQueryWrapperX<PractitionerDO> queryWrapper = new LambdaQueryWrapperX<PractitionerDO>()
                .eq(PractitionerDO::getPhone, phone)
                .eq(PractitionerDO::getDeleted, false);
        if (excludeId != null) {
            queryWrapper.ne(PractitionerDO::getId, excludeId);
        }
        if (practitionerMapper.selectCount(queryWrapper) > 0) {
            throw new RuntimeException("手机号已存在");
        }
    }

    private void validateIdCardUnique(String idCard, Long excludeId) {
        LambdaQueryWrapperX<PractitionerDO> queryWrapper = new LambdaQueryWrapperX<PractitionerDO>()
                .eq(PractitionerDO::getIdCard, idCard)
                .eq(PractitionerDO::getDeleted, false);
        if (excludeId != null) {
            queryWrapper.ne(PractitionerDO::getId, excludeId);
        }
        if (practitionerMapper.selectCount(queryWrapper) > 0) {
            throw new RuntimeException("身份证号已存在");
        }
    }

    private String updatePractitionerQualifications(Long practitionerId,
            List<PractitionerQualificationSaveReqVO> qualificationList) {
        if (qualificationList == null) {
            return null;
        }

        // 先获取阿姨的oneID
        PractitionerDO practitioner = validatePractitionerExists(practitionerId);
        StringBuilder changeLog = new StringBuilder();
        List<PractitionerQualificationDO> existingQualifications = practitionerQualificationMapper.selectList(
                new LambdaQueryWrapperX<PractitionerQualificationDO>()
                        .eq(PractitionerQualificationDO::getPractitionerOneId, practitioner.getAuntOneid())
                        .eq(PractitionerQualificationDO::getDeleted, false));

        // 处理资质文件更新
        for (PractitionerQualificationSaveReqVO qualificationVO : qualificationList) {
            if (qualificationVO.getId() != null) {
                // 更新现有资质文件
                PractitionerQualificationDO qualification = PractitionerQualificationConvert.INSTANCE
                        .convert(qualificationVO);
                qualification.setUpdateTime(new Date());
                qualification.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
                practitionerQualificationMapper.updateById(qualification);
                changeLog.append("更新了资质文件【").append(qualificationVO.getFileName()).append("】；");
            } else {
                // 新增资质文件
                PractitionerQualificationDO qualification = PractitionerQualificationConvert.INSTANCE
                        .convert(qualificationVO);
                qualification.setPractitionerOneId(practitioner.getAuntOneid());
                qualification.setCreateTime(new Date());
                qualification.setDeleted(false);
                qualification.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
                qualification.setTenantId(SecurityFrameworkUtils.getLoginUser() != null
                        ? SecurityFrameworkUtils.getLoginUser().getTenantId()
                        : 1L);
                practitionerQualificationMapper.insert(qualification);
                changeLog.append("新增了资质文件【").append(qualificationVO.getFileName()).append("】；");
            }
        }

        // 删除不存在的资质文件
        for (PractitionerQualificationDO existingQualification : existingQualifications) {
            boolean found = qualificationList.stream()
                    .anyMatch(
                            qualificationVO -> Objects.equals(qualificationVO.getId(), existingQualification.getId()));
            if (!found) {
                existingQualification.setDeleted(true);
                existingQualification.setUpdateTime(new Date());
                existingQualification.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
                practitionerQualificationMapper.updateById(existingQualification);
                changeLog.append("删除了资质文件【").append(existingQualification.getFileName()).append("】；");
            }
        }

        return changeLog.toString();
    }

    @Override
    public List<PractitionerTaskStatsRespVO> getPractitionerTaskStats(String auntOneid, String month) {
        // 验证阿姨是否存在
        PractitionerDO practitioner = practitionerMapper.selectByAuntOneId(auntOneid);
        if (practitioner == null) {
            throw new RuntimeException("阿姨不存在");
        }

        // 根据月份参数获取对应的开始和结束日期
        YearMonth targetMonth;
        if (month != null && !month.trim().isEmpty()) {
            try {
                // 解析月份参数（格式：yyyy-MM）
                targetMonth = YearMonth.parse(month);
            } catch (Exception e) {
                throw new RuntimeException("月份格式错误，请使用 yyyy-MM 格式，例如：2024-01");
            }
        } else {
            // 如果月份为空，则获取当前月份
            targetMonth = YearMonth.now();
        }

        LocalDate startDate = targetMonth.atDay(1);
        LocalDate endDate = targetMonth.atEndOfMonth();

        // 查询每日任务统计
        List<Map<String, Object>> dailyStats = domesticTaskMapper.selectDailyTaskStatsByAuntOneId(
                auntOneid, startDate, endDate);

        // 构建响应对象列表
        List<PractitionerTaskStatsRespVO> resultList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 遍历指定月份每一天
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            PractitionerTaskStatsRespVO respVO = new PractitionerTaskStatsRespVO();
            respVO.setDate(date.format(formatter));
            respVO.setTaskCount(0);

            // 查找该日期的统计数据
            for (Map<String, Object> stat : dailyStats) {
                Object scheduleDateObj = stat.get("scheduleDate");
                LocalDate statDate = null;
                
                // 处理不同类型的日期对象
                if (scheduleDateObj instanceof java.sql.Date) {
                    java.sql.Date sqlDate = (java.sql.Date) scheduleDateObj;
                    statDate = sqlDate.toLocalDate();
                } else if (scheduleDateObj instanceof java.time.LocalDate) {
                    statDate = (LocalDate) scheduleDateObj;
                } else if (scheduleDateObj instanceof java.util.Date) {
                    java.util.Date utilDate = (java.util.Date) scheduleDateObj;
                    statDate = utilDate.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                }
                
                if (statDate != null && statDate.equals(date)) {
                    Object taskCountObj = stat.get("taskCount");
                    Integer taskCount = null;
                    
                    // 处理不同类型的任务数量对象
                    if (taskCountObj instanceof Long) {
                        Long longCount = (Long) taskCountObj;
                        taskCount = longCount.intValue();
                    } else if (taskCountObj instanceof Integer) {
                        taskCount = (Integer) taskCountObj;
                    } else if (taskCountObj instanceof Number) {
                        Number numberCount = (Number) taskCountObj;
                        taskCount = numberCount.intValue();
                    }
                    
                    respVO.setTaskCount(taskCount != null ? taskCount : 0);
                    break;
                }
            }

            resultList.add(respVO);
        }

        return resultList;
    }
}
