package cn.bztmaster.cnt.module.publicbiz.service.employment;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationCreateVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationUpdateVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationBatchUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyQualificationDO;

import java.util.List;

/**
 * 机构资质文件 Service 接口
 *
 * <AUTHOR>
 */
public interface AgencyQualificationService {

    /**
     * 根据机构ID获取资质文件列表
     *
     * @param agencyId 机构ID
     * @return 资质文件列表
     */
    List<AgencyQualificationDO> getQualificationsByAgencyId(Long agencyId);

    /**
     * 创建资质文件
     *
     * @param createVO 创建请求VO
     * @return 创建的资质文件
     */
    AgencyQualificationDO createQualification(AgencyQualificationCreateVO createVO);

    /**
     * 更新资质文件
     *
     * @param updateVO 更新请求VO
     */
    void updateQualification(AgencyQualificationUpdateVO updateVO);

    /**
     * 删除资质文件
     *
     * @param id 资质文件ID
     */
    void deleteQualification(Long id);

    /**
     * 根据机构ID删除所有资质文件
     *
     * @param agencyId 机构ID
     */
    void deleteQualificationsByAgencyId(Long agencyId);

    /**
     * 根据ID获取资质文件
     *
     * @param id 资质文件ID
     * @return 资质文件
     */
    AgencyQualificationDO getQualificationById(Long id);

    /**
     * 获取机构资质文件列表（小程序端）
     *
     * @param agencyId 机构ID
     * @return 机构资质文件响应VO
     */
    cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyQualificationRespVO getAgencyQualificationsForApp(
            Long agencyId);

    /**
     * 获取机构资质文件列表（管理后台）
     *
     * @param reqVO 查询请求VO
     * @return 资质文件列表
     */
    List<AgencyQualificationListRespVO> getQualificationList(AgencyQualificationListReqVO reqVO);

    /**
     * 批量更新机构资质文件
     *
     * @param reqVO 批量更新请求VO
     */
    void batchUpdateQualifications(AgencyQualificationBatchUpdateReqVO reqVO);
}
