package cn.bztmaster.cnt.module.publicbiz.service.agency.impl;

import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyFinanceBalanceRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyFinanceMonthlyDataReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyFinanceMonthlyDataRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyFinanceStatisticsReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyFinanceStatisticsRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderPaymentDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderPaymentMapper;
import cn.bztmaster.cnt.module.publicbiz.service.agency.AgencyFinanceService;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 机构财务中心 Service 实现类
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Service
@Validated
@Slf4j
public class AgencyFinanceServiceImpl implements AgencyFinanceService {

    @Resource
    private PublicbizOrderPaymentMapper publicbizOrderPaymentMapper;

    @Override
    public AgencyFinanceBalanceRespVO getAgencyBalance(String agencyId) {
        log.info("获取机构累计结余 - agencyId: {}", agencyId);

        try {
            // 1. 参数校验
            if (StrUtil.isEmpty(agencyId)) {
                throw new ServiceException(400, "机构ID不能为空");
            }

            // 2. 查询机构成功支付的金额总和
            BigDecimal totalIncome = publicbizOrderPaymentMapper.selectPaymentAmountByAgencyIdAndStatus(
                    Long.valueOf(agencyId), "success");
            if (totalIncome == null) {
                totalIncome = BigDecimal.ZERO;
            }

            // 3. 查询机构退款金额总和
            BigDecimal totalRefund = publicbizOrderPaymentMapper.selectPaymentAmountByAgencyIdAndStatus(
                    Long.valueOf(agencyId), "refund_success");
            if (totalRefund == null) {
                totalRefund = BigDecimal.ZERO;
            }

            // 4. 计算结余金额（收入 - 退款）
            BigDecimal balanceAmount = totalIncome.subtract(totalRefund);

            // 5. 构建响应结果
            AgencyFinanceBalanceRespVO result = new AgencyFinanceBalanceRespVO();
            result.setBalanceAmount(balanceAmount.toString());
            result.setCurrency("CNY");

            log.info("获取机构累计结余成功 - agencyId: {}, balanceAmount: {}", agencyId, balanceAmount);
            return result;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取机构累计结余失败 - agencyId: {}, 错误: {}", agencyId, e.getMessage(), e);
            throw new ServiceException(500, "获取机构累计结余失败，请稍后重试");
        }
    }

    @Override
    public AgencyFinanceMonthlyDataRespVO getMonthlyData(AgencyFinanceMonthlyDataReqVO reqVO) {
        log.info("获取月度财务数据 - reqVO: {}", reqVO);

        try {
            // 1. 参数校验（@Valid注解已处理）

            // 2. 设置时间范围
            LocalDate startDate = LocalDate.of(reqVO.getYear(), reqVO.getMonth(), 1);
            LocalDate endDate = startDate.plusMonths(1).minusDays(1);
            LocalDateTime startTime = startDate.atStartOfDay();
            LocalDateTime endTime = endDate.atTime(23, 59, 59);

            // 3. 查询月度支付记录
            List<PublicbizOrderPaymentDO> paymentList = publicbizOrderPaymentMapper.selectByAgencyIdAndTimeRange(
                    Long.valueOf(reqVO.getAgencyId()), startTime, endTime);

            // 4. 构建月度汇总数据
            AgencyFinanceMonthlyDataRespVO.MonthlySummary summary = buildMonthlySummary(reqVO, paymentList);

            // 5. 构建交易明细列表
            AgencyFinanceMonthlyDataRespVO.TransactionList transactionList = buildTransactionList(reqVO, paymentList);

            // 6. 构建响应结果
            AgencyFinanceMonthlyDataRespVO result = new AgencyFinanceMonthlyDataRespVO();
            result.setSummary(summary);
            result.setTransactions(transactionList);

            log.info("获取月度财务数据成功 - agencyId: {}, year: {}, month: {}",
                    reqVO.getAgencyId(), reqVO.getYear(), reqVO.getMonth());
            return result;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取月度财务数据失败 - reqVO: {}, 错误: {}", reqVO, e.getMessage(), e);
            throw new ServiceException(500, "获取月度财务数据失败，请稍后重试");
        }
    }

    @Override
    public AgencyFinanceStatisticsRespVO getFinanceStatistics(AgencyFinanceStatisticsReqVO reqVO) {
        log.info("获取机构财务统计 - reqVO: {}", reqVO);

        try {
            // 1. 参数校验
            if (StrUtil.isEmpty(reqVO.getAgencyId())) {
                throw new ServiceException(400, "机构ID不能为空");
            }

            Integer year = reqVO.getYear();
            if (year == null) {
                year = LocalDate.now().getYear();
            }

            // 2. 查询年度统计数据
            Map<String, Object> stats = publicbizOrderPaymentMapper.selectPaymentStatsByAgencyId(
                    Long.valueOf(reqVO.getAgencyId()));
            if (stats == null) {
                stats = new HashMap<>();
            }

            // 3. 构建月度数据
            List<AgencyFinanceStatisticsRespVO.MonthlyData> monthlyDataList = buildMonthlyDataList(
                    Long.valueOf(reqVO.getAgencyId()), year);

            // 4. 构建响应结果
            AgencyFinanceStatisticsRespVO result = new AgencyFinanceStatisticsRespVO();
            result.setYear(year);
            result.setTotalIncome(stats.getOrDefault("total_amount", "0.00").toString());
            result.setTotalExpense("0.00"); // 暂时设为0，后续可根据业务需求调整
            result.setNetIncome(result.getTotalIncome()); // 净收入 = 总收入 - 总支出
            result.setMonthlyData(monthlyDataList);

            log.info("获取机构财务统计成功 - agencyId: {}, year: {}", reqVO.getAgencyId(), year);
            return result;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取机构财务统计失败 - reqVO: {}, 错误: {}", reqVO, e.getMessage(), e);
            throw new ServiceException(500, "获取机构财务统计失败，请稍后重试");
        }
    }

    /**
     * 构建月度汇总数据
     */
    private AgencyFinanceMonthlyDataRespVO.MonthlySummary buildMonthlySummary(
            AgencyFinanceMonthlyDataReqVO reqVO, List<PublicbizOrderPaymentDO> paymentList) {

        BigDecimal incomeAmount = BigDecimal.ZERO;
        BigDecimal expenseAmount = BigDecimal.ZERO;

        for (PublicbizOrderPaymentDO payment : paymentList) {
            if ("success".equals(payment.getPaymentStatus())) {
                incomeAmount = incomeAmount.add(payment.getPaymentAmount());
            } else if ("refund_success".equals(payment.getRefundStatus())) {
                expenseAmount = expenseAmount.add(payment.getRefundAmount());
            }
        }

        AgencyFinanceMonthlyDataRespVO.MonthlySummary summary = new AgencyFinanceMonthlyDataRespVO.MonthlySummary();
        summary.setYear(reqVO.getYear());
        summary.setMonth(reqVO.getMonth());
        summary.setIncomeAmount(incomeAmount.toString());
        summary.setExpenseAmount(expenseAmount.toString());
        summary.setNetAmount(incomeAmount.subtract(expenseAmount).toString());

        return summary;
    }

    /**
     * 构建交易明细列表
     */
    private AgencyFinanceMonthlyDataRespVO.TransactionList buildTransactionList(
            AgencyFinanceMonthlyDataReqVO reqVO, List<PublicbizOrderPaymentDO> paymentList) {

        List<AgencyFinanceMonthlyDataRespVO.Transaction> transactionList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (PublicbizOrderPaymentDO payment : paymentList) {
            // 根据筛选条件过滤
            if (!"all".equals(reqVO.getType())) {
                if ("income".equals(reqVO.getType()) && !"success".equals(payment.getPaymentStatus())) {
                    continue;
                }
                if ("expense".equals(reqVO.getType()) && !"refund_success".equals(payment.getRefundStatus())) {
                    continue;
                }
            }

            AgencyFinanceMonthlyDataRespVO.Transaction transaction = new AgencyFinanceMonthlyDataRespVO.Transaction();
            transaction.setId(payment.getId().toString());
            transaction.setOrderNo(payment.getOrderNo());
            transaction.setTime(payment.getPaymentTime().format(formatter));
            transaction.setPaymentMethod(payment.getPaymentType());
            transaction.setStatus(payment.getPaymentStatus());

            // 设置交易描述和金额
            if ("success".equals(payment.getPaymentStatus())) {
                transaction.setDescription("收入订单支付(" + getPaymentTypeDesc(payment.getPaymentType()) + ")");
                transaction.setAmount(payment.getPaymentAmount().toString());
                transaction.setType("income");
                transaction.setCategory("order");
            } else if ("refund_success".equals(payment.getRefundStatus())) {
                transaction.setDescription("退款(" + payment.getRefundReason() + ")");
                transaction.setAmount("-" + payment.getRefundAmount().toString());
                transaction.setType("expense");
                transaction.setCategory("refund");
            }

            transaction.setBalance("结余: ¥" + payment.getPaymentAmount().toString());

            transactionList.add(transaction);
        }

        // 分页处理
        int total = transactionList.size();
        int startIndex = (reqVO.getPageNo() - 1) * reqVO.getPageSize();
        int endIndex = Math.min(startIndex + reqVO.getPageSize(), total);

        List<AgencyFinanceMonthlyDataRespVO.Transaction> pageList = startIndex < total
                ? transactionList.subList(startIndex, endIndex)
                : new ArrayList<>();

        AgencyFinanceMonthlyDataRespVO.TransactionList result = new AgencyFinanceMonthlyDataRespVO.TransactionList();
        result.setList(pageList);
        result.setTotal((long) total);
        result.setPageNo(reqVO.getPageNo());
        result.setPageSize(reqVO.getPageSize());
        result.setTotalPages((total + reqVO.getPageSize() - 1) / reqVO.getPageSize());

        return result;
    }

    /**
     * 构建月度数据列表
     */
    private List<AgencyFinanceStatisticsRespVO.MonthlyData> buildMonthlyDataList(Long agencyId, Integer year) {
        List<AgencyFinanceStatisticsRespVO.MonthlyData> monthlyDataList = new ArrayList<>();

        for (int month = 1; month <= 12; month++) {
            LocalDate startDate = LocalDate.of(year, month, 1);
            LocalDate endDate = startDate.plusMonths(1).minusDays(1);
            LocalDateTime startTime = startDate.atStartOfDay();
            LocalDateTime endTime = endDate.atTime(23, 59, 59);

            List<PublicbizOrderPaymentDO> monthPayments = publicbizOrderPaymentMapper.selectByAgencyIdAndTimeRange(
                    agencyId, startTime, endTime);

            BigDecimal income = BigDecimal.ZERO;
            BigDecimal expense = BigDecimal.ZERO;

            for (PublicbizOrderPaymentDO payment : monthPayments) {
                if ("success".equals(payment.getPaymentStatus())) {
                    income = income.add(payment.getPaymentAmount());
                } else if ("refund_success".equals(payment.getRefundStatus())) {
                    expense = expense.add(payment.getRefundAmount());
                }
            }

            AgencyFinanceStatisticsRespVO.MonthlyData monthlyData = new AgencyFinanceStatisticsRespVO.MonthlyData();
            monthlyData.setMonth(month);
            monthlyData.setIncome(income.toString());
            monthlyData.setExpense(expense.toString());

            monthlyDataList.add(monthlyData);
        }

        return monthlyDataList;
    }

    /**
     * 获取支付方式描述
     */
    private String getPaymentTypeDesc(String paymentType) {
        if (StrUtil.isEmpty(paymentType)) {
            return "其他";
        }

        switch (paymentType) {
            case "wechat":
                return "微信支付";
            case "alipay":
                return "支付宝";
            case "cash":
                return "现金";
            case "bank_transfer":
                return "银行转账";
            case "pos":
                return "POS机刷卡";
            default:
                return "其他";
        }
    }

}
