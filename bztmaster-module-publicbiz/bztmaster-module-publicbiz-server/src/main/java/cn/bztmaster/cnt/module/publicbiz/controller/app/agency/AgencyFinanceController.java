package cn.bztmaster.cnt.module.publicbiz.controller.app.agency;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;

import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyFinanceBalanceRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyFinanceMonthlyDataReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyFinanceMonthlyDataRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyFinanceStatisticsReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyFinanceStatisticsRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.agency.AgencyFinanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 机构财务中心 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "机构端 - 机构财务中心")
@RestController
@RequestMapping("/publicbiz/agency/finance")
@Validated
@Slf4j
public class AgencyFinanceController {

    @Resource
    private AgencyFinanceService agencyFinanceService;

    @GetMapping("/balance")
    @Operation(summary = "获取机构累计结余")

    public CommonResult<AgencyFinanceBalanceRespVO> getAgencyBalance(
            @Parameter(description = "机构ID", required = true) @RequestParam("agencyId") String agencyId) {
        log.info("获取机构累计结余 - agencyId: {}", agencyId);

        AgencyFinanceBalanceRespVO result = agencyFinanceService.getAgencyBalance(agencyId);
        return success(result);
    }

    @GetMapping("/monthly-data")
    @Operation(summary = "获取月度财务数据")

    public CommonResult<AgencyFinanceMonthlyDataRespVO> getMonthlyData(
            @Valid AgencyFinanceMonthlyDataReqVO reqVO) {
        log.info("获取月度财务数据 - reqVO: {}", reqVO);

        AgencyFinanceMonthlyDataRespVO result = agencyFinanceService.getMonthlyData(reqVO);
        return success(result);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取机构财务统计")

    public CommonResult<AgencyFinanceStatisticsRespVO> getFinanceStatistics(
            @Valid AgencyFinanceStatisticsReqVO reqVO) {
        log.info("获取机构财务统计 - reqVO: {}", reqVO);

        AgencyFinanceStatisticsRespVO result = agencyFinanceService.getFinanceStatistics(reqVO);
        return success(result);
    }

}
