package cn.bztmaster.cnt.module.publicbiz.dal.mysql.order;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderPaymentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 订单支付记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PublicbizOrderPaymentMapper extends BaseMapperX<PublicbizOrderPaymentDO> {

    /**
     * 根据订单ID查询支付记录列表
     *
     * @param orderId 订单ID
     * @return 支付记录列表
     */
    default List<PublicbizOrderPaymentDO> selectByOrderId(Long orderId) {
        return selectList(PublicbizOrderPaymentDO::getOrderId, orderId);
    }

    /**
     * 根据订单号查询支付记录列表
     *
     * @param orderNo 订单号
     * @return 支付记录列表
     */
    default List<PublicbizOrderPaymentDO> selectByOrderNo(String orderNo) {
        return selectList(PublicbizOrderPaymentDO::getOrderNo, orderNo);
    }

    /**
     * 根据订单ID和支付状态查询支付记录
     *
     * @param orderId       订单ID
     * @param paymentStatus 支付状态
     * @return 支付记录列表
     */
    default List<PublicbizOrderPaymentDO> selectByOrderIdAndStatus(Long orderId, String paymentStatus) {
        return selectList(PublicbizOrderPaymentDO::getOrderId, orderId,
                PublicbizOrderPaymentDO::getPaymentStatus, paymentStatus);
    }

    /**
     * 根据支付单号查询支付记录
     *
     * @param paymentNo 支付单号
     * @return 支付记录
     */
    default PublicbizOrderPaymentDO selectByPaymentNo(String paymentNo) {
        return selectOne(PublicbizOrderPaymentDO::getPaymentNo, paymentNo);
    }

    /**
     * 根据订单ID删除支付记录（逻辑删除）
     *
     * @param orderId 订单ID
     * @return 删除记录数
     */
    default int deleteByOrderId(Long orderId) {
        return delete(PublicbizOrderPaymentDO::getOrderId, orderId);
    }

    /**
     * 根据支付状态查询支付记录列表
     *
     * @param paymentStatus 支付状态
     * @return 支付记录列表
     */
    default List<PublicbizOrderPaymentDO> selectByPaymentStatus(String paymentStatus) {
        return selectList(PublicbizOrderPaymentDO::getPaymentStatus, paymentStatus);
    }

    /**
     * 根据分账状态查询支付记录列表
     *
     * @param splitStatus 分账状态
     * @return 支付记录列表
     */
    default List<PublicbizOrderPaymentDO> selectBySplitStatus(String splitStatus) {
        return selectList(PublicbizOrderPaymentDO::getSplitStatus, splitStatus);
    }

    /**
     * 根据退款状态查询支付记录列表
     *
     * @param refundStatus 退款状态
     * @return 支付记录列表
     */
    default List<PublicbizOrderPaymentDO> selectByRefundStatus(String refundStatus) {
        return selectList(PublicbizOrderPaymentDO::getRefundStatus, refundStatus);
    }

    /**
     * 根据支付机构查询支付记录列表
     *
     * @param paymentProvider 支付机构
     * @return 支付记录列表
     */
    default List<PublicbizOrderPaymentDO> selectByPaymentProvider(String paymentProvider) {
        return selectList(PublicbizOrderPaymentDO::getPaymentProvider, paymentProvider);
    }

    /**
     * 根据第三方子商户号查询支付记录
     *
     * @param thirdPartySubMerchantId 第三方子商户号
     * @return 支付记录
     */
    default PublicbizOrderPaymentDO selectByThirdPartySubMerchantId(String thirdPartySubMerchantId) {
        return selectOne(PublicbizOrderPaymentDO::getThirdPartySubMerchantId, thirdPartySubMerchantId);
    }

    /**
     * 根据订单ID统计支付总金额
     *
     * @param orderId 订单ID
     * @return 支付总金额
     */
    default java.math.BigDecimal sumPaymentAmountByOrderId(Long orderId) {
        return selectList(PublicbizOrderPaymentDO::getOrderId, orderId)
                .stream()
                .map(PublicbizOrderPaymentDO::getPaymentAmount)
                .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);
    }
    /**
     * 根据机构ID查询支付记录列表
     *
     * @param agencyId 机构ID
     * @return 支付记录列表
     */
    default List<PublicbizOrderPaymentDO> selectByAgencyId(Long agencyId) {
        return selectList(PublicbizOrderPaymentDO::getAgencyId, agencyId);
    }

    /**
     * 根据机构名称查询支付记录列表
     *
     * @param agencyName 机构名称
     * @return 支付记录列表
     */
    default List<PublicbizOrderPaymentDO> selectByAgencyName(String agencyName) {
        return selectList(PublicbizOrderPaymentDO::getAgencyName, agencyName);
    }

    /**
     * 根据机构ID和支付状态查询支付记录列表
     *
     * @param agencyId      机构ID
     * @param paymentStatus 支付状态
     * @return 支付记录列表
     */
    default List<PublicbizOrderPaymentDO> selectByAgencyIdAndStatus(Long agencyId, String paymentStatus) {
        return selectList(PublicbizOrderPaymentDO::getAgencyId, agencyId,
                PublicbizOrderPaymentDO::getPaymentStatus, paymentStatus);
    }

    // ========== XML 映射方法 ==========

    /**
     * 根据机构ID和支付状态统计支付金额
     *
     * @param agencyId      机构ID
     * @param paymentStatus 支付状态
     * @return 支付金额
     */
    BigDecimal selectPaymentAmountByAgencyIdAndStatus(@Param("agencyId") Long agencyId,
            @Param("paymentStatus") String paymentStatus);

    /**
     * 根据机构ID查询支付统计信息
     *
     * @param agencyId 机构ID
     * @return 支付统计信息
     */
    Map<String, Object> selectPaymentStatsByAgencyId(@Param("agencyId") Long agencyId);

    /**
     * 根据机构ID和时间范围查询支付记录
     *
     * @param agencyId  机构ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 支付记录列表
     */
    List<PublicbizOrderPaymentDO> selectByAgencyIdAndTimeRange(@Param("agencyId") Long agencyId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 根据机构名称模糊查询支付记录
     *
     * @param agencyName 机构名称
     * @return 支付记录列表
     */
    List<PublicbizOrderPaymentDO> selectByAgencyNameLike(@Param("agencyName") String agencyName);

    /**
     * 根据机构ID和分账状态查询支付记录
     *
     * @param agencyId    机构ID
     * @param splitStatus 分账状态
     * @return 支付记录列表
     */
    List<PublicbizOrderPaymentDO> selectByAgencyIdAndSplitStatus(@Param("agencyId") Long agencyId,
            @Param("splitStatus") String splitStatus);

    /**
     * 根据机构ID和退款状态查询支付记录
     *
     * @param agencyId     机构ID
     * @param refundStatus 退款状态
     * @return 支付记录列表
     */
    List<PublicbizOrderPaymentDO> selectByAgencyIdAndRefundStatus(@Param("agencyId") Long agencyId,
            @Param("refundStatus") String refundStatus);

}
