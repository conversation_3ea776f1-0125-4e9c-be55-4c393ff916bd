package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order;

import cn.bztmaster.cnt.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单支付记录 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_order_payment")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PublicbizOrderPaymentDO extends TenantBaseDO {

    /**
     * 支付记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String paymentNo;

    /**
     * 支付类型：cash-现金/wechat-微信支付/alipay-支付宝/bank_transfer-银行转账/pos-POS机刷卡/other-其他
     */
    private String paymentType;

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 支付状态：pending-待支付/success-支付成功/failed-支付失败/cancelled-已取消
     */
    private String paymentStatus;

    /**
     * 支付时间
     */
    private LocalDateTime paymentTime;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 支付备注
     */
    private String paymentRemark;

    /**
     * 第三方交易号
     */
    private String transactionId;

    /**
     * 资金托管:0 直接付；1 安心付
     */
    private Boolean isEscrow;

    /**
     * 资金托管:平台支付单号（第二阶段支付）
     */
    private String platformPaymentNo;

    /**
     * 资金托管:平台支付状态
     */
    private String platformPaymentStatus;

    /**
     * 资金托管:平台支付时间
     */
    private LocalDateTime platformPaymentTime;

    /**
     * 收款方用户ID（服务者）
     */
    private Long receiverId;

    /**
     * 回调状态：pending/success/failed
     */
    private String notifyStatus;

    /**
     * 通联回调时间
     */
    private LocalDateTime notifyTime;

    /**
     * 通联返回渠道信息:支付渠道（如微信/支付宝/银联）
     */
    private String channelType;

    /**
     * 通联返回渠道信息:银行编码
     */
    private String bankCode;

    /**
     * 通联返回渠道信息:付款方账号（如银行卡号/OpenID）
     */
    private String payerAccount;

    /**
     * 支付机构：UNIONPAY-银联商务/TONGLIAN-通联支付
     */
    private String paymentProvider;

    /**
     * 第三方子商户号（银联/通联的机构收款账户ID）
     */
    private String thirdPartySubMerchantId;

    /**
     * 通联支付编码（如WX_JSAPI/ALIPAY_H5）
     */
    private String tppCode;

    /**
     * 第三方分账批次号（银联split_batch_no/通联batchNo）
     */
    private String thirdPartySplitBatchNo;

    /**
     * 分账金额（支付金额-平台手续费）
     */
    private BigDecimal splitAmount;

    /**
     * 平台手续费（元）
     */
    private BigDecimal platformFee;

    /**
     * 分账状态：pending-待分账/success-分账成功/failed-分账失败
     */
    private String splitStatus;

    /**
     * 分账失败原因（如"账户冻结"）
     */
    private String splitFailReason;

    /**
     * 分账方类型（通联：PLATFORM-平台/MERCHANT-商户）
     */
    private String splitPartyType;

    /**
     * 平台退款单号
     */
    private String refundNo;

    /**
     * 退款金额（元）
     */
    private BigDecimal refundAmount;

    /**
     * 退款状态：none-未退款/pending-退款中/success-退款成功/failed-退款失败
     */
    private String refundStatus;

    /**
     * 退款完成时间
     */
    private LocalDateTime refundTime;

    /**
     * 第三方退款交易号（银联/通联退款ID）
     */
    private String thirdPartyRefundId;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 第三方回调原始内容（JSON格式）
     */
    private String notifyContent;

    /**
     * 机构ID
     */
    private Long agencyId;

    /**
     * 机构名称
     */
    private String agencyName;

}
