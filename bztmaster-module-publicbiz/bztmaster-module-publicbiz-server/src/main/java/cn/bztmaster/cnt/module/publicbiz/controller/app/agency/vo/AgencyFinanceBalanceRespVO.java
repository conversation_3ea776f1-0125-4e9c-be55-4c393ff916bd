package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 机构财务结余 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构端 - 机构财务结余 Response VO")
@Data
public class AgencyFinanceBalanceRespVO {

    @Schema(description = "累计结余金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "34260.00")
    private String balanceAmount;

    @Schema(description = "货币类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "CNY")
    private String currency;

}
