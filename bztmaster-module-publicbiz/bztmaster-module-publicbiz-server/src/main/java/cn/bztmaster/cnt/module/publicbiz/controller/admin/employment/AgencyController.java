package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyCreateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyStatisticsReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyStatisticsRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyTrendRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationListRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.employment.AgencyService;
import cn.bztmaster.cnt.module.publicbiz.service.employment.AgencyQualificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 机构管理 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 机构管理")
@RestController
@RequestMapping("/publicbiz/agency")
@Validated
public class AgencyController {

    @Resource
    private AgencyService agencyService;

    @Resource
    private AgencyQualificationService agencyQualificationService;

    @GetMapping("/page")
    @Operation(summary = "分页查询机构列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency:query')")
    public CommonResult<PageResult<AgencyRespVO>> pageAgency(@Valid AgencyPageReqVO reqVO) {
        PageResult<AgencyRespVO> pageResult = agencyService.pageAgency(reqVO);
        return success(pageResult);
    }

    @GetMapping("/list")
    @Operation(summary = "查询机构列表（不分页）")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency:query')")
    public CommonResult<List<AgencyRespVO>> listAgency(@Valid AgencyListReqVO reqVO) {
        List<AgencyRespVO> agencyList = agencyService.listAgency(reqVO);
        return success(agencyList);
    }

    @GetMapping("/get/{id}")
    @Operation(summary = "获取机构详情")
    @Parameter(name = "id", description = "机构ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency:query')")
    public CommonResult<AgencyRespVO> getAgency(@PathVariable("id") Long id) {
        AgencyRespVO agency = agencyService.getAgency(id);
        return success(agency);
    }

    @PutMapping("/update")
    @Operation(summary = "更新机构审核状态")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency:update')")
    public CommonResult<Boolean> updateAgency(@Valid @RequestBody AgencyUpdateReqVO reqVO) {
        agencyService.updateAgency(reqVO);
        return success(true);
    }

    @PostMapping("/create")
    @Operation(summary = "新增机构")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency:create')")
    public CommonResult<Long> createAgency(@Valid @RequestBody AgencyCreateReqVO reqVO) {
        Long agencyId = agencyService.createAgency(reqVO);
        return success(agencyId);
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除机构")
    @Parameter(name = "id", description = "机构ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency:delete')")
    public CommonResult<Boolean> deleteAgency(@PathVariable("id") Long id) {
        agencyService.deleteAgency(id);
        return success(true);
    }

    @GetMapping("/statistics")
    @Operation(summary = "统计机构业务数据")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency:query')")
    public CommonResult<AgencyStatisticsRespVO> getAgencyStatistics(@Valid AgencyStatisticsReqVO reqVO) {
        AgencyStatisticsRespVO result = agencyService.getAgencyStatistics(reqVO);
        return success(result);
    }

    @GetMapping("/trend/{id}")
    @Operation(summary = "获取机构业务趋势数据")
    @Parameter(name = "id", description = "机构ID", required = true, example = "1")
    public CommonResult<AgencyTrendRespVO> getAgencyTrendData(@PathVariable("id") Long id) {
        AgencyTrendRespVO result = agencyService.getAgencyTrendData(id);
        return success(result);
    }

    @GetMapping("/qualification/list")
    @Operation(summary = "获取机构资质文件列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency:query')")
    public CommonResult<List<AgencyQualificationListRespVO>> getQualificationList(@Valid AgencyQualificationListReqVO reqVO) {
        List<AgencyQualificationListRespVO> result = agencyQualificationService.getQualificationList(reqVO);
        return success(result);
    }
}