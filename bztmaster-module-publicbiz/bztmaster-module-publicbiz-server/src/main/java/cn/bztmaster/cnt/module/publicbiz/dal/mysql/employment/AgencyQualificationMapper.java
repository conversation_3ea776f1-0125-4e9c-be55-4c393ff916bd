package cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyQualificationDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 机构资质文件 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AgencyQualificationMapper extends BaseMapperX<AgencyQualificationDO> {

    default List<AgencyQualificationDO> selectListByAgencyId(Long agencyId) {
        return selectList(new LambdaQueryWrapperX<AgencyQualificationDO>()
                .eq(AgencyQualificationDO::getAgencyId, agencyId)
                .eq(AgencyQualificationDO::getStatus, 1)
                .orderByAsc(AgencyQualificationDO::getSortOrder));
    }

    default void deleteByAgencyId(Long agencyId) {
        delete(new LambdaQueryWrapperX<AgencyQualificationDO>()
                .eq(AgencyQualificationDO::getAgencyId, agencyId));
    }

    default List<AgencyQualificationDO> selectListByAgencyIdAndFileTypes(Long agencyId, List<String> fileTypes) {
        LambdaQueryWrapperX<AgencyQualificationDO> wrapper = new LambdaQueryWrapperX<AgencyQualificationDO>()
                .eq(AgencyQualificationDO::getAgencyId, agencyId)
                .eq(AgencyQualificationDO::getStatus, 1);

        if (fileTypes != null && !fileTypes.isEmpty()) {
            wrapper.in(AgencyQualificationDO::getFileType, fileTypes);
        }

        return selectList(wrapper.orderByAsc(AgencyQualificationDO::getSortOrder));
    }

    default AgencyQualificationDO selectByAgencyIdAndFileType(Long agencyId, String fileType) {
        return selectOne(new LambdaQueryWrapperX<AgencyQualificationDO>()
                .eq(AgencyQualificationDO::getAgencyId, agencyId)
                .eq(AgencyQualificationDO::getFileType, fileType)
                .eq(AgencyQualificationDO::getStatus, 1));
    }

    default void deleteByAgencyIdAndFileType(Long agencyId, String fileType) {
        delete(new LambdaQueryWrapperX<AgencyQualificationDO>()
                .eq(AgencyQualificationDO::getAgencyId, agencyId)
                .eq(AgencyQualificationDO::getFileType, fileType));
    }

    default void deleteByAgencyIdAndFileTypes(Long agencyId, List<String> fileTypes) {
        if (fileTypes != null && !fileTypes.isEmpty()) {
            delete(new LambdaQueryWrapperX<AgencyQualificationDO>()
                    .eq(AgencyQualificationDO::getAgencyId, agencyId)
                    .in(AgencyQualificationDO::getFileType, fileTypes));
        }
    }
}